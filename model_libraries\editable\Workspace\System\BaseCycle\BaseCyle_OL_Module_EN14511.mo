within Workspace.System.BaseCycle;
model BaseCyle_OL_Module_EN14511
  extends.Workspace.System.BaseCycle.BaseCycle_OL_Module(
    BPHEECOA(
      isEcoOff=isOffECOA,
      Z_UA_expression=
        if use_Calib then
          calibrationBlock.Z_U_Eco_A
        else
          1.0),
    BPHEECOB(
      isEcoOff=isOffECOB,
      Z_UA_expression=
        if use_Calib then
          calibrationBlock.Z_U_Eco_B
        else
          1.0),
    CompressorA(
      isEcoOff=isOffECOA,
      Z_flow_suc_expression=
        if use_Calib then
          calibrationBlock.Z_Flow_A
        else
          1.0,
      Z_power_expression=
        if use_Calib then
          calibrationBlock.Z_Power_A
        else
          1.0),
    CompressorB(
      isEcoOff=isOffECOB,
      Z_flow_suc_expression=
        if use_Calib then
          calibrationBlock.Z_Flow_B
        else
          1.0,
      Z_power_expression=
        if use_Calib then
          calibrationBlock.Z_Power_B
        else
          1.0),
    evaporator(
      Z_Uev1_expression=
        if use_Calib then
          calibrationBlock.Z_Evap_A
        else
          1.0,
      Z_Uev2_expression=
        if use_Calib then
          calibrationBlock.Z_Evap_B
        else
          1.0,
      X=globalParameters.Evap_X,
      CoolantMedium=globalParameters.EvapCoolantMedium,
      Z_dpc_expression=
        if use_Calib then
          calibrationBlock.Z_Evap_dpc
        else
          1.0),
    motorA(
      use_z_power_expression=true,
      Z_power_expression=
        if use_Calib then
          calibrationBlock.Zfan_A
        else
          1.0),
    motorB(
      use_z_power_expression=true,
      Z_power_expression=
        if use_Calib then
          calibrationBlock.Zfan_B
        else
          1.0),
    sourceAirA(
      Tdb_set=OAT,
      RH_set=relative_humidity),
    sourceAirB(
      Tdb_set=OAT,
      RH_set=relative_humidity),
    globalParameters(
      EvapCoolantMedium=CoolantMedium,
      Evap_X=BrineConcentration,
      capacity_design=capacity_design),
    NodeSinkEvap(
      X=globalParameters.Evap_X,
      CoolantMedium=globalParameters.EvapCoolantMedium),
    pipeDuplex(
      X=globalParameters.Evap_X,
      CoolantMedium=globalParameters.EvapCoolantMedium,
      isOff=false),
    port_b(
      Xi_set=globalParameters.Evap_X,
      CoolantMedium=globalParameters.EvapCoolantMedium),
    port_a(
      CoolantMedium=globalParameters.EvapCoolantMedium,
      Xi_set=globalParameters.Evap_X),
    NodeSourceEvap(
      X=globalParameters.Evap_X,
      CoolantMedium=globalParameters.EvapCoolantMedium),
    splitUserPump(
      X=globalParameters.Evap_X,
      CoolantMedium=globalParameters.EvapCoolantMedium),
    pumpUser(
      X=globalParameters.Evap_X,
      CoolantMedium=globalParameters.EvapCoolantMedium),
    mixerPumpUser(
      X=globalParameters.Evap_X,
      CoolantMedium=globalParameters.EvapCoolantMedium),
    External_System(
      X=globalParameters.Evap_X,
      CoolantMedium=globalParameters.EvapCoolantMedium));
  .Workspace.Auxiliary.Block_EN14511.EN14511 en14511(
    isOffB=isOffB,
    isOffA=isOffA,
    nFanB=n_coilsB,
    nFanA=n_coilsA,
    is_HR=false,
    integrated_pump=isUserPumpPresent)
    annotation (Placement(transformation(extent={{-429.00274387426725,556.9972561257327},{-330.99725612573275,655.0027438742673}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.BusinessFactors_30XBV.BusinessFactors_cooling businessFactors(
    c_load_bf_cap=c_load_bf_cap,
    c_const_bf_cap=c_const_bf_cap,
    c_load2_bf_cap=c_load2_bf_cap,
    c_load2_bf_pow=c_load2_bf_pow,
    c_load_bf_pow=c_load_bf_pow,
    c_const_bf_pow=c_const_bf_pow,
    bf_cap_max=bf_cap_max,
    bf_cap_min=bf_cap_min,
    bf_pow_max=bf_pow_max,
    bf_pow_min=bf_pow_min,
    is_fixedSpeed=is_fixedSpeed,
    c_load2_bf_pow_opt17A=c_load2_bf_pow_opt17A,
    c_load_bf_pow_opt17A=c_load_bf_pow_opt17A,
    c_const_bf_pow_opt17A=c_const_bf_pow_opt17A,
    bf_pow_max_opt17A=bf_pow_max_opt17A,
    bf_pow_min_opt17A=bf_pow_min_opt17A)
    annotation (Placement(transformation(extent={{-230.78709315670147,435.21290684329847},{-177.21290684329853,488.78709315670153}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression FanscalingA(
    y=n_coilsA)
    annotation (Placement(transformation(extent={{-518.1040983388306,700.3959251968458},{-490.5076663670519,728.4981924502126}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression FanscalingB(
    y=n_coilsB)
    annotation (Placement(transformation(extent={{-518.1040983388306,678.3959251968458},{-490.5076663670519,706.4981924502126}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression airDP_A(
    y=0)
    annotation (Placement(transformation(extent={{-518.1040983388306,658.3959251968458},{-490.5076663670519,686.4981924502126}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression airDP_B(
    y=0)
    annotation (Placement(transformation(extent={{-518.1040983388306,639.3662522909406},{-490.5076663670519,665.5278653561179}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression brineDPe(
    y=pipeDuplex.summary.dp)
    annotation (Placement(transformation(extent={{-518.1040983388306,618.6488428376399},{-490.5076663670519,646.2452748094186}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression brineDPi(
    y=-evaporator.summary.dp_coolant)
    annotation (Placement(transformation(extent={{-518.1040983388306,598.6488428376399},{-490.5076663670519,626.2452748094186}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression brineFlow(
    y=NodeSinkEvap.summary.Vd)
    annotation (Placement(transformation(extent={{-518.1040983388306,578.6488428376399},{-490.5076663670519,606.2452748094186}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression airFlowA(
    y=sinkAirA.summary.Vd_flow)
    annotation (Placement(transformation(extent={{-518.1040983388306,558.6488428376399},{-490.5076663670519,586.2452748094186}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression airFlowB(
    y=sinkAirB.summary.Vd_flow)
    annotation (Placement(transformation(extent={{-518.1040983388306,538.6488428376399},{-490.5076663670519,566.2452748094186}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression grossCoolCap(
    y=NodeSourceEvap.port_a.m_flow * (NodeSourceEvap.summary.h - NodeSinkEvap.summary.h))
    annotation (Placement(transformation(extent={{-517.7982159858893,518.2017840141107},{-490.2017840141107,545.7982159858893}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression grossPower(
    y=systemVariables.summary.pow_total)
    annotation (Placement(transformation(extent={{-517.7982159858893,498.2017840141107},{-490.2017840141107,525.7982159858893}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression grossHeatCap(
    y=0)
    annotation (Placement(transformation(extent={{-517.5292670595516,478.47073294044856},{-490.47073294044844,505.52926705955144}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression brineDPi_cond(
    y=0)
    annotation (Placement(transformation(extent={{-517.8351494124928,458.9177917639778},{-490.7766152933897,485.9763258830807}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression brineFlow_cond(
    y=0)
    annotation (Placement(transformation(extent={{-517.8351494124928,436.9177917639778},{-490.7766152933897,463.9763258830807}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression load(
    y=(CompressorB.summary.Ncomp+CompressorA.summary.Ncomp)/(NcompAMax+NcompBMax))
    annotation (Placement(transformation(extent={{-519.7633285747384,419.6470353609774},{-492.0484361311442,444.18041561941436}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.RealExpression EcoFlux_A(
    y=BPHEECOA.summary.Q_flow)
    annotation (Placement(transformation(extent={{-64.19005062017283,403.4338966027532},{-57.661923064037694,409.9620241588883}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EcoArea_A(
    y=BPHEECOA.summary.surface_area_H)
    annotation (Placement(transformation(extent={{-64.19005062017283,392.37613349335675},{-57.661923064037694,398.904261049492}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EcoArea_B(
    y=BPHEECOB.summary.surface_area_H)
    annotation (Placement(transformation(extent={{-64.19005062017283,386.8472519386586},{-57.661923064037694,393.3753794947937}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST_B(
    y=nodeevapoutB.summary.Tsat-273.15)
    annotation (Placement(transformation(extent={{-64.19005062017283,375.78948882926215},{-57.661923064037694,382.3176163853974}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SDT_A(
    y=nodeOilsepoutA.summary.Tsat-273.15)
    annotation (Placement(transformation(extent={{-64.19005062017283,370.260607274564},{-57.661923064037694,376.7887348306991}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SDT_B(
    y=nodeOilsepoutB.summary.Tsat-273.15)
    annotation (Placement(transformation(extent={{-63.264063778067566,366.7359362219324},{-56.735936221932434,373.2640637780676}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression FrqComp_A(
    y=CompressorA.summary.Ncomp)
    annotation (Placement(transformation(extent={{-63.264063778067566,360.73593622193243},{-56.735936221932434,367.26406377806757}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression FrqComp_B(
    y=CompressorB.summary.Ncomp)
    annotation (Placement(transformation(extent={{-63.264063778067566,354.73593622193243},{-56.735936221932434,361.26406377806757}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression nCoil_A(
    y=n_coilsA)
    annotation (Placement(transformation(extent={{-63.44926114648862,348.0524230543379},{-56.921133590353485,354.58055061047315}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression nCoil_B(
    y=n_coilsB)
    annotation (Placement(transformation(extent={{-63.44926114648862,342.52354149963975},{-56.921133590353485,349.0516690557749}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression mFlowCoil_A(
    y=nodecondAirinA.summary.m_flow)
    annotation (Placement(transformation(extent={{-63.44926114648862,336.9946599449416},{-56.921133590353485,343.5227875010767}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression mFlowCoil_B(
    y=nodecondAirinB.summary.m_flow)
    annotation (Placement(transformation(extent={{-63.44926114648862,331.4657783902433},{-56.921133590353485,337.99390594637845}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression frq_fan_A
    annotation (Placement(transformation(extent={{-3.264063778067566,-3.264063778067566},{3.264063778067566,3.264063778067566}},origin={-12.0,426.00000000000006},rotation=-90.0)));
  .Modelica.Blocks.Sources.RealExpression frq_fan_B
    annotation (Placement(transformation(extent={{-3.2640637780675656,-3.264063778067566},{3.2640637780675656,3.264063778067566}},origin={-6.300047839014205,426.1928780941917},rotation=-90.0)));
  .Modelica.Blocks.Sources.RealExpression Re(
    y=evaporator.summary.Re_a_coolant)
    annotation (Placement(transformation(extent={{-64.19005062017283,408.96277815745145},{-57.661923064037694,415.4909057135866}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Evap_LWT(
    y=NodeSinkEvap.summary.T)
    annotation (Placement(transformation(extent={{-3.264063778067566,-3.264063778067566},{3.264063778067566,3.264063778067566}},origin={-18.0,426.00000000000006},rotation=-90.0)));
  .Workspace.Auxiliary.Calibration.CalibrationBlock calibrationBlock(
    CompressorA=CompType_CKA,
    CompressorB=CompType_CKB,
    is_CoatingOption=isCoating,
    longEvap=longEvap)
    annotation (Placement(transformation(extent={{-29.158726429794875,356.84127357020503},{13.158726429794875,399.15872642979497}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EcoFlux_B(
    y=BPHEECOB.summary.Q_flow)
    annotation (Placement(transformation(extent={{-64.19005062017283,397.905015048055},{-57.661923064037694,404.43314260419015}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SST_A(
    y=nodeevapoutA.summary.Tsat-273.15)
    annotation (Placement(transformation(extent={{-64.19005062017283,381.3183703839604},{-57.661923064037694,387.84649794009556}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput actuatorSSTmaxA
    annotation (Placement(transformation(extent={{5.874783593258314,5.874783593258314},{-5.874783593258314,-5.874783593258314}},rotation=180.0,origin={-64.0,463.0}),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,10})));
  .Modelica.Blocks.Interfaces.RealInput actuatorSSTmaxB
    annotation (Placement(transformation(extent={{6.498213986187395,6.498213986187409},{-6.498213986187395,-6.498213986187409}},rotation=180.0,origin={-64.0,449.0}),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-110})));
  .Modelica.Blocks.Sources.RealExpression evap_capacity1(
    y=evaporator.summary.capacity1)
    annotation (Placement(transformation(extent={{-63.264063778067566,324.73593622193243},{-56.735936221932434,331.26406377806757}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression evap_capacity2(
    y=evaporator.summary.capacity2)
    annotation (Placement(transformation(extent={{-63.264063778067566,318.73593622193243},{-56.735936221932434,325.26406377806757}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression scalingFactorA(
    y=FanA.Scaling_Factor_summary)
    annotation (Placement(transformation(extent={{-63.264063778067566,312.73593622193243},{-56.735936221932434,319.26406377806757}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression scalingFactorB(
    y=FanB.Scaling_Factor_summary)
    annotation (Placement(transformation(extent={{-63.264063778067566,306.73593622193243},{-56.735936221932434,313.26406377806757}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression FrqComp_A_Max(
    y=NcompAMax)
    annotation (Placement(transformation(extent={{-63.04526722842917,299.4377127077372},{-56.517139672294036,305.96584026387234}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression FrqComp_B_Max(
    y=NcompBMax)
    annotation (Placement(transformation(extent={{-63.40462059047596,293.307990025145},{-56.876493034340825,299.8361175812801}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Q_flow_air_A(
    y=condAirA.summary.Q_flow_air)
    annotation (Placement(transformation(extent={{-3.264063778067566,-3.264063778067566},{3.264063778067566,3.264063778067566}},origin={-0.910094039076931,426.7295071433202},rotation=-90.0)));
  .Modelica.Blocks.Sources.RealExpression Q_flow_air_B(
    y=condAirB.summary.Q_flow_air)
    annotation (Placement(transformation(extent={{-3.264063778067566,-3.264063778067566},{3.264063778067566,3.264063778067566}},origin={4.415053511126475,426.29506296409596},rotation=-90.0)));
  parameter Real c_load_bf_cap=0.1
    "capacity business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_const_bf_cap=0.92
    "capacity business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_load2_bf_cap=0.92
    "capacity business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_load2_bf_pow=-0.114
    "power business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_load_bf_pow=0.239
    "power business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_const_bf_pow=0.876
    "power business factor coefficient"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_cap_max=1
    "capacity business factor max value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_cap_min=1
    "capacity business factor min value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_pow_max=1
    "power business factor max value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_pow_min=1
    "power business factor min value"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_load2_bf_pow_opt17A=-0.114
    "power business factor coefficient when opt17A is selected"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_load_bf_pow_opt17A=0.239
    "power business factor coefficient when opt17A is selected"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real c_const_bf_pow_opt17A=0.876
    "power business factor coefficient when opt17A is selected"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_pow_max_opt17A=1
    "power business factor max value when opt17A is selected"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  parameter Real bf_pow_min_opt17A=1
    "power business factor min value when opt17A is selected"
    annotation (Dialog(group="businessFactors_coef",tab="BusinessFactor&EN14511"));
  .Modelica.Blocks.Sources.RealExpression OAT_C(
    y=sourceAirA.summary.Tdb-273.15)
    annotation (Placement(transformation(extent={{-63.264063778067566,286.73593622193243},{-56.735936221932434,293.26406377806757}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression OAT_degC(
    y=sourceAirA.summary.Tdb-273.15)
    annotation (Placement(transformation(extent={{-521.7477408739279,398.252259126072},{-498.25225912607203,421.747740873928}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(grossPower.y,businessFactors.gross_pow)
    annotation (Line(points={{-488.82196241552174,512},{-301.90588235294126,512},{-301.90588235294126,459.3212906843298},{-225.42967452536118,459.3212906843298}},color={0,0,127}));
  connect(grossCoolCap.y,businessFactors.gross_cool_cap)
    annotation (Line(points={{-488.82196241552174,532},{-301.90588235294126,532},{-301.90588235294126,451.2851627373194},{-225.42967452536118,451.2851627373194}},color={0,0,127}));
  connect(FanscalingA.y,en14511.fanScalingA)
    annotation (Line(points={{-489.127844768463,714.4470588235292},{-458.21823549783574,714.4470588235292},{-458.21823549783574,642.009850197425},{-429.00274387426725,642.009850197425}},color={0,0,127}));
  connect(FanscalingB.y,en14511.fanScalingB)
    annotation (Line(points={{-489.127844768463,692.4470588235292},{-458.21823549783574,692.4470588235292},{-458.21823549783574,634.2669995161891},{-429.00274387426725,634.2669995161891}},color={0,0,127}));
  connect(airDP_A.y,en14511.DPe_air_A)
    annotation (Line(points={{-489.127844768463,672.4470588235292},{-458.21823549783574,672.4470588235292},{-458.21823549783574,619.5555832218408},{-429.00274387426725,619.5555832218408}},color={0,0,127}));
  connect(airDP_B.y,en14511.DPe_air_B)
    annotation (Line(points={{-489.127844768463,652.4470588235292},{-458.21823549783574,652.4470588235292},{-458.21823549783574,611.812732540605},{-429.00274387426725,611.812732540605}},color={0,0,127}));
  connect(brineFlow.y,en14511.q)
    annotation (Line(points={{-489.127844768463,592.4470588235292},{-458.30588235294124,592.4470588235292},{-458.30588235294124,597.8756013143803},{-429.00274387426725,597.8756013143803}},color={0,0,127}));
  connect(airFlowA.y,en14511.q_air_A)
    annotation (Line(points={{-489.127844768463,572.4470588235292},{-458.21823549783574,572.4470588235292},{-458.21823549783574,590.1327506331445},{-429.00274387426725,590.1327506331445}},color={0,0,127}));
  connect(airFlowB.y,en14511.q_air_B)
    annotation (Line(points={{-489.127844768463,552.4470588235292},{-458.21823549783574,552.4470588235292},{-458.21823549783574,582.3898999519085},{-429.00274387426725,582.3898999519085}},color={0,0,127}));
  connect(grossCoolCap.y,en14511.inst_gross_cap)
    annotation (Line(points={{-488.82196241552174,532},{-458.21823549783574,532},{-458.21823549783574,574.6470492706727},{-429.00274387426725,574.6470492706727}},color={0,0,127}));
  connect(grossPower.y,en14511.inst_gross_pow)
    annotation (Line(points={{-488.82196241552174,512},{-458.21823549783574,512},{-458.21823549783574,566.9041985894368},{-429.00274387426725,566.9041985894368}},color={0,0,127}));
  connect(grossHeatCap.y,en14511.inst_gross_heat)
    annotation (Line(points={{-489.1178062344933,492},{-458.36615740732157,492},{-458.36615740732157,559.1613479082009},{-429.00274387426725,559.1613479082009}},color={0,0,127}));
  connect(brineDPi_cond.y,en14511.DPi_cond)
    annotation (Line(points={{-489.4236885874345,472.44705882352923},{-458.36615740732157,472.44705882352923},{-458.36615740732157,550.6442121588415},{-429.00274387426725,550.6442121588415}},color={0,0,127}));
  connect(brineFlow_cond.y,en14511.q_cond)
    annotation (Line(points={{-489.4236885874345,450.44705882352923},{-458.36615740732157,450.44705882352923},{-458.36615740732157,542.1270764094819},{-429.00274387426725,542.1270764094819}},color={0,0,127}));
  connect(en14511.inst_net_cap,businessFactors.net_cool_cap)
    annotation (Line(points={{-330.12893414372235,608.3280823814172},{-301.90588235294126,608.3280823814172},{-301.90588235294126,483.42967452536124},{-225.42967452536118,483.42967452536124}},color={0,0,127}));
  connect(en14511.inst_net_pow,businessFactors.net_pow)
    annotation (Line(points={{-330.12893414372235,593.627259219137},{-301.59023712514727,593.627259219137},{-301.59023712514727,475.39354657835077},{-225.42967452536118,475.39354657835077}},color={0,0,127}));
  connect(EcoFlux_A.y,calibrationBlock.EcoFlux_A)
    annotation (Line(points={{-57.33551668623096,406.69796038082075},{-57.33551668623096,396.3326820000715},{-24.9269811438359,396.3326820000715}},color={0,0,127}));
  connect(EcoFlux_B.y,calibrationBlock.EcoFlux_B)
    annotation (Line(points={{-57.33551668623096,401.1690788261226},{-57.33551668623096,392.5241112427084},{-24.9269811438359,392.5241112427084}},color={0,0,127}));
  connect(EcoArea_A.y,calibrationBlock.EcoArea_A)
    annotation (Line(points={{-57.33551668623096,395.64019727142437},{-57.33551668623096,388.7155404853453},{-24.9269811438359,388.7155404853453}},color={0,0,127}));
  connect(EcoArea_B.y,calibrationBlock.EcoArea_B)
    annotation (Line(points={{-57.33551668623096,390.11131571672615},{-57.33551668623096,385.3301442565781},{-24.9269811438359,385.3301442565781}},color={0,0,127}));
  connect(SST_A.y,calibrationBlock.SST_A)
    annotation (Line(points={{-57.33551668623096,384.582434162028},{-57.33551668623096,381.521573499215},{-24.9269811438359,381.521573499215}},color={0,0,127}));
  connect(SST_B.y,calibrationBlock.SST_B)
    annotation (Line(points={{-57.33551668623096,379.0535526073298},{-57.33551668623096,378.13617727044783},{-24.9269811438359,378.13617727044783}},color={0,0,127}));
  connect(SDT_A.y,calibrationBlock.SDT_A)
    annotation (Line(points={{-57.33551668623096,373.52467105263156},{-57.33551668623096,374.32760651308473},{-24.9269811438359,374.32760651308473}},color={0,0,127}));
  connect(SDT_B.y,calibrationBlock.SDT_B)
    annotation (Line(points={{-56.40952984412568,370},{-56.40952984412568,371.3653848129134},{-24.9269811438359,371.3653848129134}},color={0,0,127}));
  connect(FrqComp_A.y,calibrationBlock.FrqComp_A)
    annotation (Line(points={{-56.40952984412568,364},{-56.40952984412568,367.9799885841462},{-24.9269811438359,367.9799885841462}},color={0,0,127}));
  connect(FrqComp_B.y,calibrationBlock.FrqComp_B)
    annotation (Line(points={{-56.40952984412568,358},{-56.40952984412568,364.1714178267831},{-24.9269811438359,364.1714178267831}},color={0,0,127}));
  connect(nCoil_A.y,calibrationBlock.nCoil_A)
    annotation (Line(points={{-56.59472721254673,351.31648683240553},{-56.59472721254673,360.78602159801596},{-24.9269811438359,360.78602159801596}},color={0,0,127}));
  connect(nCoil_B.y,calibrationBlock.nCoil_B)
    annotation (Line(points={{-56.59472721254673,345.7876052777073},{-56.59472721254673,356.97745084065286},{-24.9269811438359,356.97745084065286}},color={0,0,127}));
  connect(mFlowCoil_B.y,calibrationBlock.mflowCoil_B)
    annotation (Line(points={{-56.59472721254673,334.7298421683109},{-56.59472721254673,349.78348385452256},{-24.9269811438359,349.78348385452256}},color={0,0,127}));
  connect(mFlowCoil_A.y,calibrationBlock.mflowCoil_A)
    annotation (Line(points={{-56.59472721254673,340.25872372300915},{-56.59472721254673,353.59205461188566},{-24.9269811438359,353.59205461188566}},color={0,0,127}));
  connect(frq_fan_B.y,calibrationBlock.frq_fan_B)
    annotation (Line(points={{-6.300047839014205,422.6024079383174},{-6.300047839014205,411.85396228767195},{-6.30730188561641,411.85396228767195}},color={0,0,127}));
  connect(Re.y,calibrationBlock.Re)
    annotation (Line(points={{-57.33551668623096,412.226841935519},{-57.33551668623096,400.5644272860305},{-24.9269811438359,400.5644272860305}},color={0,0,127}));
  connect(Evap_LWT.y,calibrationBlock.LWT)
    annotation (Line(points={{-18,422.40952984412576},{-18,411.85396228767195},{-16.88666510051385,411.85396228767195}},color={0,0,127}));
  connect(frq_fan_A.y,calibrationBlock.frq_fan_A)
    annotation (Line(points={{-12,422.40952984412576},{-12,425.78248355263156},{-11.808570757363077,425.78248355263156},{-11.808570757363077,411.85396228767195}},color={0,0,127}));
  connect(calibrationBlock.SSTmaxB,actuatorSSTmaxB)
    annotation (Line(points={{-24.9269811438359,403.9000382761393},{-42,403.9000382761393},{-42,449},{-64,449}},color={0,0,127}));
  connect(actuatorSSTmaxA,calibrationBlock.SSTmaxA)
    annotation (Line(points={{-64,463},{-38,463},{-38,407.19904247311706},{-24.9269811438359,407.19904247311706}},color={0,0,127}));
  connect(brineDPi.y,en14511.DPi)
    annotation (Line(points={{-489.127844768463,612.4470588235292},{-458.21823549783574,612.4470588235292},{-458.21823549783574,604.8441669274927},{-429.00274387426725,604.8441669274927}},color={0,0,127}));
  connect(brineDPe.y,en14511.DPe)
    annotation (Line(points={{-489.127844768463,632.4470588235292},{-458.21823549783574,632.4470588235292},{-458.21823549783574,627.2984339030768},{-429.00274387426725,627.2984339030768}},color={0,0,127}));
  connect(evap_capacity1.y,calibrationBlock.evap_capacity1)
    annotation (Line(points={{-56.40952984412568,328},{-40.66825549398079,328},{-40.66825549398079,344.99238676951984},{-24.9269811438359,344.99238676951984}},color={0,0,127}));
  connect(evap_capacity2.y,calibrationBlock.evap_capacity2)
    annotation (Line(points={{-56.40952984412568,322},{-40.66825549398079,322},{-40.66825549398079,340.76064148356085},{-24.9269811438359,340.76064148356085}},color={0,0,127}));
  connect(FrqComp_A_Max.y,calibrationBlock.FrqComp_A_Max)
    annotation (Line(points={{-56.19073329448728,302.70177648580477},{-40.545786607661455,302.70177648580477},{-40.545786607661455,329.8367930826138},{-24.900839920835626,329.8367930826138}},color={0,0,127}));
  connect(FrqComp_B_Max.y,calibrationBlock.FrqComp_B_Max)
    annotation (Line(points={{-56.55008665653407,296.57205380321255},{-40.633039150774685,296.57205380321255},{-40.633039150774685,326.847500302033},{-24.715991645015304,326.847500302033}},color={0,0,127}));
  connect(calibrationBlock.Q_flow_air_A,Q_flow_air_A.y)
    annotation (Line(points={{-1.3245055553874998,411.84475070834674},{-1.3245055553874998,418.8421091234603},{-0.9100940390769302,418.8421091234603},{-0.9100940390769302,423.1390369874459}},color={0,0,127}));
  connect(calibrationBlock.Q_flow_air_B,Q_flow_air_B.y)
    annotation (Line(points={{2.8262894565823835,411.89496779461143},{2.8262894565823835,422.70459280822166},{4.415053511126476,422.70459280822166}},color={0,0,127}));
  connect(load.y,businessFactors.load)
    annotation (Line(points={{-490.6626915089645,431.9137254901959},{-486.0484361311442,431.9137254901959},{-486.0484361311442,434.67716498016443},{-225.42967452536118,434.67716498016443}},color={0,0,127}));
  connect(scalingFactorA.y,calibrationBlock.scalingFactor_A)
    annotation (Line(points={{-56.40952984412568,316},{-40.66825549398079,316},{-40.66825549398079,336.10572166900596},{-24.9269811438359,336.10572166900596}},color={0,0,127}));
  connect(scalingFactorB.y,calibrationBlock.scalingFactor_B)
    annotation (Line(points={{-56.40952984412568,310},{-40.66825549398079,310},{-40.66825549398079,333.1434999688347},{-24.9269811438359,333.1434999688347}},color={0,0,127}));
  connect(OAT_C.y,calibrationBlock.OAT)
    annotation (Line(points={{-56.40952984412568,290},{-40.45666822968284,290},{-40.45666822968284,323.410485811129},{-24.503806615240002,323.410485811129}},color={0,0,127}));
  connect(OAT_degC.y,businessFactors.OAT_C)
    annotation (Line(points={{-497.07748503867924,410},{-362.82342142388467,410},{-362.82342142388467,425.0338114437519},{-225.42967452536118,425.0338114437519}},color={0,0,127}));
end BaseCyle_OL_Module_EN14511;
