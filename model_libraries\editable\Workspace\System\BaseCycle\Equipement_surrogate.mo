within Workspace.System.BaseCycle;
model Equipement_surrogate
  extends.Workspace.System.BaseCycle.BaseCyle_OL_Module_EN14511(
    redeclare model MCHX=.Workspace.Surrogate.CondAirM_Surrogate,
    condAirA(
      Tsat_in_start=globalParameters.Tsat_cond_start,
      Z_U_fixed=true,
      selector=.Workspace.Surrogate.MCHX_Cond.Selector.MCHX_Cond_R134a,
      Z_dpr_fixed=true,
      use_ZU_in=true,
      use_Zdpr_in=true,
      x_out_start=0.2,
      use_Z_fanScaling=true),
    condAirB(
      Tsat_in_start=globalParameters.Tsat_cond_start,
      Z_U_fixed=true,
      selector=.Workspace.Surrogate.MCHX_Cond.Selector.MCHX_Cond_R134a,
      use_ZU_in=true,
      use_Zdpr_in=true,
      x_out_start=0.2,
      use_Z_fanScaling=true),
    frq_fan_B(
      y=Fan_crkB),
    frq_fan_A(
      y=Fan_crkA),
    businessFactors(
      use_business_factor=use_bf),
    FanA(
      use_scalingFactor_in=true,
      use_nStage_in=false),
    FanB(
      use_nStage_in=false,
      use_scalingFactor_in=true));
  .Modelica.Blocks.Sources.RealExpression Z_Cond_A(
    y=
      if use_Calib then
        calibrationBlock.Z_Cond_A
      else
        1)
    annotation (Placement(transformation(extent={{-136.0,198.0},{-156.0,218.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Z_Cond_Dpr_A(
    y=
      if use_Calib then
        calibrationBlock.Z_Cond_Dpr_A
      else
        1)
    annotation (Placement(transformation(extent={{-136.0,182.0},{-156.0,202.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression scA(
    y=nodecondAiroutA.summary.dTsh)
    annotation (Placement(transformation(extent={{178.08516318035063,466.4219844065909},{199.5932580777115,487.93007930395174}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dshA(
    y=nodeCpoutA.summary.dTsh)
    annotation (Placement(transformation(extent={{178.08516318035063,485.7792698142157},{199.5932580777115,507.2873647115765}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression eshA(
    y=nodeBPHEECOoutA.summary.dTsh)
    annotation (Placement(transformation(extent={{178.08516318035063,507.2873647115765},{199.5932580777115,528.7954596089374}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sshA(
    y=nodeCpinA.summary.dTsh)
    annotation (Placement(transformation(extent={{178.08516318035063,526.6446501192013},{199.5932580777115,548.1527450165622}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dgtA(
    y=nodeCpoutA.summary.T)
    annotation (Placement(transformation(extent={{175.93435369061456,554.6051734857705},{197.44244858797543,576.1132683831314}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sdtA(
    y=nodeCpoutA.summary.Tsat)
    annotation (Placement(transformation(extent={{175.93435369061456,571.8116494036592},{197.44244858797543,593.3197443010201}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sstA(
    y=nodeCpinA.summary.Tsat)
    annotation (Placement(transformation(extent={{175.93435369061456,591.168934811284},{197.44244858797543,612.6770297086449}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_levelB(
    y=evaporator.summary.relLevel_topBundle2)
    annotation (Placement(transformation(extent={{489.95253919208335,612.6770297086448},{468.44444429472253,634.1851246060057}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sstB(
    y=nodeCpinB.summary.Tsat)
    annotation (Placement(transformation(extent={{490.7540474486804,593.2459525513195},{469.2459525513196,614.7540474486805}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sdtB(
    y=nodeCpoutB.summary.Tsat)
    annotation (Placement(transformation(extent={{489.95253919208335,573.9624588933953},{468.44444429472253,595.4705537907562}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dgtB(
    y=nodeCpoutB.summary.T)
    annotation (Placement(transformation(extent={{489.95253919208335,556.7559829755065},{468.44444429472253,578.2640778728675}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sshB(
    y=nodeCpinB.summary.dTsh)
    annotation (Placement(transformation(extent={{492.1033486818195,528.7954596089374},{470.5952537844586,550.3035545062984}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression eshB(
    y=nodeBPHEECOoutB.summary.dTsh)
    annotation (Placement(transformation(extent={{492.75404744868047,509.24595255131953},{471.24595255131953,530.7540474486805}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dshB(
    y=nodeCpoutB.summary.dTsh)
    annotation (Placement(transformation(extent={{492.1033486818195,487.93007930395174},{470.5952537844586,509.4381742013127}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression scB(
    y=nodecondAiroutB.summary.dTsh)
    annotation (Placement(transformation(extent={{492.1033486818195,468.57279389632697},{470.5952537844586,490.0808887936879}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression totalCapacity(
    y=controlledCapacity)
    annotation (Placement(transformation(extent={{321.1139942478005,605.9557500532196},{342.62208914516134,627.4638449505806}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression oat(
    y=sourceAirA.summary.Tdb)
    annotation (Placement(transformation(extent={{323.2459525513196,631.2459525513195},{344.7540474486804,652.7540474486805}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ewt(
    y=NodeSourceEvap.summary.T)
    annotation (Placement(transformation(extent={{321.6516966202345,570.6018190656827},{343.1597915175954,592.1099139630436}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression lwt(
    y=NodeSinkEvap.summary.T)
    annotation (Placement(transformation(extent={{321.1139942478005,552.1855128098174},{342.62208914516134,573.6936077071783}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBusA
    annotation (Placement(transformation(extent={{266.2683522595302,432.8155861294645},{309.284542054252,475.8317759241863}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-55.913,90.783},{-15.913,130.783}})));
  .Workspace.Interfaces.MeasurementBus measurementBusB
    annotation (Placement(transformation(extent={{361.97937455278617,433.89099087433254},{404.9955643475079,476.9071806690543}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{18.087,90.783},{58.087,130.783}})));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_levelA(
    y=evaporator.summary.relLevel_topBundle1)
    annotation (Placement(transformation(extent={{174.8589489457465,610.2573690326917},{196.36704384310738,631.7654639300526}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.FanSignal.FanSignalProcessor fanSignalProcessorB(
    is_fixedSpeed=is_fixedSpeed,
    max_fan_rpm=max_fan_rpm,
    max_motor_frequency=max_motor_frequency,
    limit_max_rpm=limit_max_fan_rpm,
    limit_max_frequency=max_motor_frequency)
    annotation (Placement(transformation(extent={{295.57207172919914,348.92792827080086},{264.92792827080086,379.57207172919914}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput actuatorSSTmaxA
    annotation (Placement(transformation(extent={{5.874783593258314,5.874783593258314},{-5.874783593258314,-5.874783593258314}},rotation=180,origin={-56,455.5}),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,10})));
  .Modelica.Blocks.Interfaces.RealInput actuatorSSTmaxB
    annotation (Placement(transformation(extent={{6.498213986187395,6.498213986187409},{-6.498213986187395,-6.498213986187409}},rotation=180,origin={-56,440.5}),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-110})));
  .Modelica.Blocks.Interfaces.RealInput Fan_crkA
    annotation (Placement(transformation(extent={{-339.0608674607705,346.9390771820128},{-328.9391325392295,357.0609228179872}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,50})));
  .Modelica.Blocks.Interfaces.RealInput EXV_Main_crkA
    annotation (Placement(transformation(extent={{-5.06086746077051,-5.060922817987198},{5.06086746077051,5.060922817987198}},origin={-266.0,10.0},rotation=-180.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,30})));
  .Modelica.Blocks.Interfaces.RealInput EXV_Eco_crkA
    annotation (Placement(transformation(extent={{-5.06086746077051,-5.060922817987205},{5.06086746077051,5.060922817987205}},origin={-267.55041541123245,68.33836039010768},rotation=-180.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-10})));
  .Modelica.Blocks.Interfaces.RealInput Speed_crkA
    annotation (Placement(transformation(extent={{5.06086746077051,-5.060922817987205},{-5.06086746077051,5.060922817987205}},origin={-67.94199049538275,135.9784124244304},rotation=135.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-30})));
  .Modelica.Blocks.Interfaces.RealInput Speed_crkB
    annotation (Placement(transformation(extent={{-5.06086746077051,-5.060922817987205},{5.06086746077051,5.060922817987205}},origin={58.12343618597351,132.9639100482761},rotation=-135.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-50})));
  .Modelica.Blocks.Interfaces.RealInput Fan_crkB
    annotation (Placement(transformation(extent={{324.8108674607705,366.6890771820128},{314.6891325392295,376.8109228179872}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-70})));
  .Modelica.Blocks.Interfaces.RealInput EXV_Main_crkB
    annotation (Placement(transformation(extent={{5.06086746077051,-5.060922817987198},{-5.06086746077051,5.060922817987198}},origin={264.3903160233064,2.084734796069114},rotation=-180.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-90})));
  .Modelica.Blocks.Interfaces.RealInput EXV_Eco_crkB
    annotation (Placement(transformation(extent={{5.529477325586697,-5.529537808592238},{-5.529477325586697,5.529537808592238}},origin={263.59757900582645,68.08473479606911},rotation=-180.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-130})));
  .Modelica.Blocks.Interfaces.RealInput ActuatorPumpUser
    annotation (Placement(transformation(extent={{-436.9391325392295,10.939077182012795},{-447.0608674607705,21.060922817987205}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,70})));
  .Modelica.Blocks.Interfaces.RealInput ActuatorKaInput
    annotation (Placement(transformation(extent={{-92.93913253922949,-1.0609228179872048},{-103.06086746077051,9.060922817987205}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,90})));
  .Modelica.Blocks.Sources.RealExpression Vflow_coolant_pumpUser(
    y=NodeSourceEvap.summary.Vd)
    annotation (Placement(transformation(extent={{323.2459525513196,525.2459525513195},{344.7540474486804,546.7540474486805}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression External_Pressure(
    y=
      if isUserPumpPresent then
        External_System.dp_value.y
      else
        NodeSourceEvap.summary.p)
    annotation (Placement(transformation(extent={{321.2459525513196,499.24595255131953},{342.7540474486804,520.7540474486805}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean use_bf
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  //"if false BF's are set to 1 and controlledCapacity = engineering capacity"    
  parameter Boolean use_en
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  //"if false controlledCapacity = gross capacity" ;
  .Modelica.SIunits.Power controlledCapacity;
  .Modelica.SIunits.Power controlledPower;
  Real controlledEfficiency;
  .Modelica.SIunits.Power summary_capacity_measurement=controlledCapacity;
  .Workspace.Auxiliary.FanSignal.FanSignalProcessor fanSignalProcessorA(
    is_fixedSpeed=is_fixedSpeed,
    max_fan_rpm=max_fan_rpm,
    max_motor_frequency=max_motor_frequency,
    limit_max_rpm=limit_max_fan_rpm,
    limit_max_frequency=max_motor_frequency)
    annotation (Placement(transformation(extent={{-307.32207172919914,330.67792827080086},{-276.67792827080086,361.32207172919914}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Z_Cond_B(
    y=
      if use_Calib then
        calibrationBlock.Z_Cond_B
      else
        1)
    annotation (Placement(transformation(extent={{128.0,201.0},{148.0,221.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Z_Cond_Dpr_B(
    y=
      if use_Calib then
        calibrationBlock.Z_Cond_Dpr_B
      else
        1)
    annotation (Placement(transformation(extent={{128.0,184.0},{148.0,204.0}},origin={0.0,0.0},rotation=0.0)));
equation
  controlledCapacity=
    if use_en then
      if use_bf then
        businessFactors.pub_net_cool_cap
      else
        en14511.inst_net_cap
    else
      if use_bf then
        businessFactors.pub_gross_cool_cap
      else
        en14511.inst_gross_cap;
  controlledEfficiency=
    if use_en then
      if use_bf then
        businessFactors.pub_net_cool_cap/businessFactors.pub_net_pow
      else
        en14511.inst_net_cap/en14511.inst_net_pow
    else
      if use_bf then
        businessFactors.pub_gross_cool_cap/businessFactors.pub_gross_pow
      else
        en14511.inst_gross_cap/en14511.inst_gross_pow;
  controlledPower=
    if use_en then
      if use_bf then
        businessFactors.pub_net_pow
      else
        en14511.inst_net_pow
    else
      if use_bf then
        businessFactors.pub_gross_pow
      else
        en14511.inst_gross_pow;
  connect(sstA.y,measurementBusA.T_sst)
    annotation (Line(points={{198.51785333284346,601.9229822599644},{220.02594823020434,601.9229822599644},{220.02594823020434,454.3236810268254},{287.7764471568911,454.3236810268254}},color={0,0,127}));
  connect(sdtA.y,measurementBusA.T_sdt)
    annotation (Line(points={{198.51785333284346,582.5656968523397},{220.02594823020434,582.5656968523397},{220.02594823020434,454.3236810268254},{287.7764471568911,454.3236810268254}},color={0,0,127}));
  connect(dgtA.y,measurementBusA.T_dgt)
    annotation (Line(points={{198.51785333284346,565.359220934451},{220.02594823020434,565.359220934451},{220.02594823020434,454.3236810268254},{287.7764471568911,454.3236810268254}},color={0,0,127}));
  connect(sshA.y,measurementBusA.dT_ssh)
    annotation (Line(points={{200.66866282257956,537.3986975678818},{220.02594823020434,537.3986975678818},{220.02594823020434,454.3236810268254},{287.7764471568911,454.3236810268254}},color={0,0,127}));
  connect(eshA.y,measurementBusA.dT_esh)
    annotation (Line(points={{200.66866282257956,518.041412160257},{220.02594823020434,518.041412160257},{220.02594823020434,454.3236810268254},{287.7764471568911,454.3236810268254}},color={0,0,127}));
  connect(dshA.y,measurementBusA.dT_dsh)
    annotation (Line(points={{200.66866282257956,496.53331726289616},{220.02594823020434,496.53331726289616},{220.02594823020434,454.3236810268254},{287.7764471568911,454.3236810268254}},color={0,0,127}));
  connect(scA.y,measurementBusA.dT_sbc)
    annotation (Line(points={{200.66866282257956,477.1760318552714},{220.02594823020434,477.1760318552714},{220.02594823020434,454.3236810268254},{287.7764471568911,454.3236810268254}},color={0,0,127}));
  connect(lwt.y,measurementBusA.T_lwt)
    annotation (Line(points={{343.6974938900294,562.9395602584979},{308,562.9395602584979},{308,454.3236810268254},{287.7764471568911,454.3236810268254}},color={0,0,127}));
  connect(ewt.y,measurementBusA.T_ewt)
    annotation (Line(points={{344.23519626246343,581.3558665143631},{308.20913730938395,581.3558665143631},{308.20913730938395,454.3236810268254},{287.7764471568911,454.3236810268254}},color={0,0,127}));
  connect(oat.y,measurementBusA.T_oat)
    annotation (Line(points={{345.82945219354843,642},{308.20913730938395,642},{308.20913730938395,454.3236810268254},{287.7764471568911,454.3236810268254}},color={0,0,127}));
  connect(lwt.y,measurementBusB.T_lwt)
    annotation (Line(points={{343.6974938900294,562.9395602584979},{359.82856506305006,562.9395602584979},{359.82856506305006,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(ewt.y,measurementBusB.T_ewt)
    annotation (Line(points={{344.23519626246343,581.3558665143631},{359.82856506305006,581.3558665143631},{359.82856506305006,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(oat.y,measurementBusB.T_oat)
    annotation (Line(points={{345.82945219354843,642},{359.82856506305006,642},{359.82856506305006,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(scB.y,measurementBusB.dT_sbc)
    annotation (Line(points={{469.51984903959055,479.32684134500744},{452.31337312170183,479.32684134500744},{452.31337312170183,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(dshB.y,measurementBusB.dT_dsh)
    annotation (Line(points={{469.51984903959055,498.6841267526322},{452.31337312170183,498.6841267526322},{452.31337312170183,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(eshB.y,measurementBusB.dT_esh)
    annotation (Line(points={{470.1705478064515,520},{452.31337312170183,520},{452.31337312170183,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(sshB.y,measurementBusB.dT_ssh)
    annotation (Line(points={{469.51984903959055,539.5495070576179},{452.31337312170183,539.5495070576179},{452.31337312170183,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(dgtB.y,measurementBusB.T_dgt)
    annotation (Line(points={{467.36903954985445,567.510030424187},{452.31337312170183,567.510030424187},{452.31337312170183,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(sdtB.y,measurementBusB.T_sdt)
    annotation (Line(points={{467.36903954985445,584.7165063420757},{452.31337312170183,584.7165063420757},{452.31337312170183,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(sstB.y,measurementBusB.T_sst)
    annotation (Line(points={{468.17054780645157,604},{452.31337312170183,604},{452.31337312170183,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(rel_cooler_levelB.y,measurementBusB.rel_cooler_level)
    annotation (Line(points={{467.36903954985445,623.4310771573253},{452.31337312170183,623.4310771573253},{452.31337312170183,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(rel_cooler_levelA.y,measurementBusA.rel_cooler_level)
    annotation (Line(points={{197.44244858797543,621.0114164813722},{220.02594823020434,621.0114164813722},{220.02594823020434,454.3236810268254},{287.7764471568911,454.3236810268254}},color={0,0,127}));
  connect(Fan_crkB,fanSignalProcessorB.ControllerSignal)
    annotation (Line(points={{319.75,371.75},{295.57207172919914,371.75},{295.57207172919914,370.6852701262636}},color={0,0,127}));
  connect(calibrationBlock.SSTmaxB,actuatorSSTmaxB)
    annotation (Line(points={{-24.9269811438359,403.9000382761393},{-40.46349057191795,403.9000382761393},{-40.46349057191795,440.5},{-56,440.5}},color={0,0,127}));
  connect(actuatorSSTmaxA,calibrationBlock.SSTmaxA)
    annotation (Line(points={{-56,455.5},{-34,455.5},{-34,407.19904247311706},{-24.9269811438359,407.19904247311706}},color={0,0,127}));
  connect(EXV_Eco_crkA,EXVECOA.openCommand)
    annotation (Line(points={{-267.55041541123245,68.33836039010768},{-267.55041541123245,68},{-261.9,68}},color={0,0,127}));
  connect(EXV_Main_crkA,EXVMainA.openCommand)
    annotation (Line(points={{-266,10},{-261.9,10}},color={0,0,127}));
  connect(EXVMainB.openCommand,EXV_Main_crkB)
    annotation (Line(points={{257.9,1.9999999999999991},{264.3903160233064,2.084734796069114}},color={0,0,127}));
  connect(EXVECOB.openCommand,EXV_Eco_crkB)
    annotation (Line(points={{257.9,68},{263.59757900582645,68.08473479606911}},color={0,0,127}));
  connect(Speed_crkB,CompressorB.N_speed)
    annotation (Line(points={{58.12343618597351,132.9639100482761},{58.12343618597351,136.0755262161046},{61.11909060795743,136.0755262161046},{61.11909060795743,139.18714238393312}},color={0,0,127}));
  connect(Speed_crkA,CompressorA.N_speed)
    annotation (Line(points={{-67.94199049538275,135.9784124244304},{-71.5296104182744,135.9784124244304},{-71.5296104182744,141.83224494300265}},color={0,0,127}));
  connect(Fan_crkA,fanSignalProcessorA.ControllerSignal)
    annotation (Line(points={{-334,352},{-320.66103586459957,352},{-320.66103586459957,352.4352701262636},{-307.32207172919914,352.4352701262636}},color={0,0,127}));
  connect(condAirB.ZU_in,Z_Cond_B.y)
    annotation (Line(points={{171.1,223.1},{160.05,223.1},{160.05,211},{149,211}},color={0,0,127}));
  connect(condAirA.Zdpr_in,Z_Cond_Dpr_A.y)
    annotation (Line(points={{-173.1,225.1},{-165.05,225.1},{-165.05,192},{-157,192}},color={0,0,127}));
  connect(condAirB.Zdpr_in,Z_Cond_Dpr_B.y)
    annotation (Line(points={{171.1,223.1},{160.05,223.1},{160.05,194},{149,194}},color={0,0,127}));
  connect(condAirA.ZU_in,Z_Cond_A.y)
    annotation (Line(points={{-173.1,225.1},{-165.05,225.1},{-165.05,208},{-157,208}},color={0,0,127}));
  connect(fanSignalProcessorA.SetPoint_ACmotor,motorA.frequency_in)
    annotation (Line(points={{-278.51657687830476,356.1125673412714},{-241.13684769568636,356.1125673412714},{-241.13684769568636,338.21121186492957}},color={0,0,127}));
  connect(fanSignalProcessorA.SetPoint_fan,motorA.Scaling_motor_in)
    annotation (Line(points={{-278.2101354437208,351.82238725709567},{-236.99287161432008,351.82238725709567},{-236.99287161432008,338.21121186492957}},color={0,0,127}));
  connect(fanSignalProcessorA.SetPoint_fan,FanA.Scaling_Factor)
    annotation (Line(points={{-278.2101354437208,351.82238725709567},{-198.84544134306984,351.82238725709567},{-198.84544134306984,326.0958778187007}},color={0,0,127}));
  connect(fanSignalProcessorA.SetPoint_fan,condAirA.Z_fanScaling)
    annotation (Line(points={{-278.2101354437208,351.82238725709567},{-136,351.82238725709567},{-136,221.1},{-173.1,221.1}},color={0,0,127}));
  connect(fanSignalProcessorB.SetPoint_ACmotor,motorB.frequency_in)
    annotation (Line(points={{266.76657687830476,374.3625673412714},{246.0492639255063,374.3625673412714},{246.0492639255063,355.2609810755825}},color={0,0,127}));
  connect(fanSignalProcessorB.SetPoint_fan,motorB.Scaling_motor_in)
    annotation (Line(points={{266.4601354437208,370.07238725709567},{241.37549777521232,370.07238725709567},{241.37549777521232,355.2609810755825}},color={0,0,127}));
  connect(fanSignalProcessorB.SetPoint_fan,FanB.Scaling_Factor)
    annotation (Line(points={{266.4601354437208,370.07238725709567},{190,370.07238725709567},{190,323.1968718354271},{202.97706240811655,323.1968718354271}},color={0,0,127}));
  connect(fanSignalProcessorB.SetPoint_fan,condAirB.Z_fanScaling)
    annotation (Line(points={{266.4601354437208,370.07238725709567},{130,370.07238725709567},{130,219.1},{171.1,219.1}},color={0,0,127}));
  connect(External_System.Ka_in,ActuatorKaInput)
    annotation (Line(points={{-148.4,-16.4},{-148.4,-9.2},{-98,-9.2},{-98,4}},color={0,0,127}));
  connect(Vflow_coolant_pumpUser.y,measurementBusA.Vflow_coolant_pumpUser)
    annotation (Line(points={{345.82945219354843,536},{360,536},{360,454},{288,454}},color={0,0,127}));
  connect(External_Pressure.y,measurementBusA.External_Pressure)
    annotation (Line(points={{343.82945219354843,510},{360,510},{360,454},{288,454}},color={0,0,127}));
  connect(External_Pressure.y,measurementBusB.External_Pressure)
    annotation (Line(points={{343.82945219354843,510},{360,510},{360,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(Vflow_coolant_pumpUser.y,measurementBusB.Vflow_coolant_pumpUser)
    annotation (Line(points={{345.82945219354843,536},{360,536},{360,455.3990857716934},{383.48746945014705,455.3990857716934}},color={0,0,127}));
  connect(shaftPumpUser.speed_in,ActuatorPumpUser)
    annotation (Line(points={{-524,-44.4},{-530,-44.4},{-530,16},{-442,16}},color={0,0,127}));
    connect(totalCapacity.y,measurementBusA.capacity) annotation(Line(points = {{343.69749389002936,616.7097975019001},{308,616.7097975019001},{308,454.3236810268254},{287.7764471568911,454.3236810268254}},color = {0,0,127}));

  annotation (
    Icon(
      graphics={
        Rectangle(
          origin={1,-3},
          extent={{-107,103},{107,-103}},
          fillPattern=FillPattern.Solid,
          fillColor={186,171,208}),
        Text(
          textString="MODULE",
          origin={0,12},
          extent={{-102,48},{102,-48}})}));
end Equipement_surrogate;
