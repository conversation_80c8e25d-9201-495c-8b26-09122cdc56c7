model CalibrationBlock
  parameter.Workspace.Controller.Components.Types.CompressorSelector CompressorA=.Workspace.Controller.Components.Types.CompressorSelector.NG1
    annotation (Dialog(group="Compressor Type",tab="Compressor"));
  parameter.Workspace.Controller.Components.Types.CompressorSelector CompressorB=.Workspace.Controller.Components.Types.CompressorSelector.NG1
    annotation (Dialog(group="Compressor Type",tab="Compressor"));
  parameter.Workspace.Controller.Components.Types.RefrigerantSelector RefrigerantType=.Workspace.Controller.Components.Types.RefrigerantSelector.R134a
    annotation (Dialog(group="Refrigerant Type",tab="Evaporator"));

  Real load_A;
  Real load_B;
  Real LWT_C;
  Real factor_Zpower_LWT;
  Real factor_Zpower_OAT;
  Real factor_highLWT;
  Real factorHighOAT;
  //Input
  .Modelica.Blocks.Interfaces.RealInput Re
    annotation (Placement(transformation(extent={{-89.72283959637137,96.92075901954556},{-70.27716040362863,116.36643821228833}},origin={0.0,0.0},rotation=0.0)),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={40,40}));
  .Modelica.Blocks.Interfaces.RealInput EcoFlux_A
    annotation (Placement(transformation(extent={{-89.72283959637137,76.92075901954557},{-70.27716040362863,96.36643821228832}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput EcoFlux_B
    annotation (Placement(transformation(extent={{-89.72283959637137,58.92075901954556},{-70.27716040362863,78.36643821228833}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput EcoArea_A
    annotation (Placement(transformation(extent={{-89.72283959637137,40.92075901954557},{-70.27716040362863,60.366438212288315}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput EcoArea_B
    annotation (Placement(transformation(extent={{-89.72283959637137,24.92075901954557},{-70.27716040362863,44.366438212288315}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SST_A
    annotation (Placement(transformation(extent={{-89.72283959637137,6.920759019545567},{-70.27716040362863,26.366438212288312}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SST_B
    annotation (Placement(transformation(extent={{-89.72283959637137,-9.079240980454436},{-70.27716040362863,10.366438212288315}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SDT_A
    annotation (Placement(transformation(extent={{-89.72283959637137,-27.07924098045444},{-70.27716040362863,-7.633561787711681}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SDT_B
    annotation (Placement(transformation(extent={{-89.72283959637137,-41.07924098045443},{-70.27716040362863,-21.633561787711685}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput FrqComp_B
    annotation (Placement(transformation(extent={{-89.72283959637137,-75.07924098045443},{-70.27716040362863,-55.633561787711685}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput FrqComp_A
    annotation (Placement(transformation(extent={{-89.72283959637137,-57.07924098045443},{-70.27716040362863,-37.633561787711685}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput nCoil_B
    annotation (Placement(transformation(extent={{-89.72283959637137,-109.07924098045443},{-70.27716040362863,-89.63356178771168}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput nCoil_A
    annotation (Placement(transformation(extent={{-89.72283959637137,-91.07924098045443},{-70.27716040362863,-71.63356178771168}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput mflowCoil_B
    annotation (Placement(transformation(extent={{-89.72283959637137,-143.07924098045441},{-70.27716040362863,-123.63356178771164}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput mflowCoil_A
    annotation (Placement(transformation(extent={{-89.72283959637137,-125.07924098045443},{-70.27716040362863,-105.63356178771168}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SSTmaxA
    annotation (Placement(transformation(extent={{-89.1642454197746,128.8357545802254},{-70.8357545802254,147.1642454197746}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput SSTmaxB
    annotation (Placement(transformation(extent={{-88.9625350495647,113.44576944870516},{-71.0374649504353,131.37083954783458}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput LWT
    annotation (Placement(transformation(extent={{-9.164245419774602,-9.164245419774602},{9.164245419774602,9.164245419774602}},origin={-42.0,160.0},rotation=-90.0)));
  .Modelica.Blocks.Interfaces.RealInput frq_fan_A
    annotation (Placement(transformation(extent={{-9.164245419774602,-9.164245419774602},{9.164245419774602,9.164245419774602}},origin={-18.0,160.0},rotation=-90.0)));
  .Modelica.Blocks.Interfaces.RealInput frq_fan_B
    annotation (Placement(transformation(extent={{-9.164245419774602,-9.164245419774602},{9.164245419774602,9.164245419774602}},origin={8.0,160.0},rotation=-90.0)));
  .Modelica.Blocks.Interfaces.RealInput evap_capacity1
    annotation (Placement(transformation(extent={{-89.72283959637137,-165.7228395963714},{-70.27716040362863,-146.2771604036286}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput evap_capacity2
    annotation (Placement(transformation(extent={{-89.72283959637137,-185.7228395963714},{-70.27716040362863,-166.2771604036286}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput scalingFactor_A
    annotation (Placement(transformation(extent={{-89.72283959637137,-207.7228395963714},{-70.27716040362863,-188.2771604036286}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput scalingFactor_B
    annotation (Placement(transformation(extent={{-89.72283959637137,-221.7228395963714},{-70.27716040362863,-202.2771604036286}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput FrqComp_A_Max
    annotation (Placement(transformation(extent={{-89.59929140887998,-237.35093941226816},{-70.15361221613723,-217.9052602195254}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput FrqComp_B_Max
    annotation (Placement(transformation(extent={{-88.72566474503681,-251.4788822754613},{-69.27998555229406,-232.03320308271853}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput Q_flow_air_A
    annotation (Placement(transformation(extent={{-9.164245419774602,-9.164245419774602},{9.164245419774602,9.164245419774602}},origin={31.549604210640652,159.9564643961167},rotation=-90.0)));
  .Modelica.Blocks.Interfaces.RealInput Q_flow_air_B
    annotation (Placement(transformation(extent={{-9.164245419774602,-9.164245419774602},{9.164245419774602,9.164245419774602}},origin={51.16701845219396,160.19379950431104},rotation=-90.0)));
  //Output
  .Modelica.Blocks.Interfaces.RealOutput Z_Evap_A
    annotation (Placement(transformation(extent={{218.0,118.70769230769233},{238.0,138.70769230769233}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Evap_B
    annotation (Placement(transformation(extent={{218.0,100.70769230769233},{238.0,120.70769230769233}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Evap_dpc
    annotation (Placement(transformation(extent={{218.0,82.0},{238.0,102.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Cond_A
    annotation (Placement(transformation(extent={{218.0,46.0},{238.0,66.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Cond_B
    annotation (Placement(transformation(extent={{218.0,64.0},{238.0,84.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_U_Eco_A
    annotation (Placement(transformation(extent={{218.0,24.707692307692305},{238.0,44.707692307692305}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_U_Eco_B
    annotation (Placement(transformation(extent={{218.0,6.048340730102151},{238.0,26.04834073010215}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Power_A
    annotation (Placement(transformation(extent={{218.0,-13.292307692307695},{238.0,6.707692307692305}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Power_B
    annotation (Placement(transformation(extent={{218.0,-31.292307692307695},{238.0,-11.292307692307695}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Flow_A
    annotation (Placement(transformation(extent={{218.0,-49.292307692307695},{238.0,-29.292307692307695}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Flow_B
    annotation (Placement(transformation(extent={{218.0,-67.29230769230767},{238.0,-47.29230769230768}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Cond_Dpr_A
    annotation (Placement(transformation(extent={{218.0,-85.29230769230767},{238.0,-65.29230769230767}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Z_Cond_Dpr_B
    annotation (Placement(transformation(extent={{218.0,-107.29230769230767},{238.0,-87.29230769230767}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Zfan_A
    annotation (Placement(transformation(extent={{218.0,-123.6},{238.0,-103.6}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput Zfan_B
    annotation (Placement(transformation(extent={{218.0,-139.6},{238.0,-119.6}},rotation=0.0,origin={0.0,0.0})));
  //Evap // R134a
  parameter Real a0_evap=6.59
    annotation (Dialog(group="Coefficients ZUev",tab="Evaporator"));
  parameter Real a1_evap=-2.057e-2
    annotation (Dialog(group="Coefficients ZUev",tab="Evaporator"));
  parameter Real a_dp=1.0
    annotation (Dialog(group="Coefficients Zdp",tab="Evaporator"));
  parameter Real b_dp=0.0
    annotation (Dialog(group="Coefficients Zdp",tab="Evaporator"));
  parameter Boolean longEvap=true
    annotation (Dialog(group="Characteristics",tab="Evaporator"));
  // ZFan
  parameter Real slope_fan=-0.17
    "Slope for Zfactor law for fan motor AC"
    annotation (Dialog(tab="Fan"));
  parameter Real intercept_fan=1.25
    "Interception for linear law for fan motor AC"
    annotation (Dialog(tab="Fan"));
  //Eco 05/12/2023
  parameter Real a0_eco=1.517e-1
    annotation (Dialog(group="STD",tab="Eco"));
  parameter Real a1_eco=2.284e-5
    annotation (Dialog(group="STD",tab="Eco"));
  parameter Real a2_eco=0
    annotation (Dialog(group="STD",tab="Eco"));
  parameter Real a3_eco=0
    annotation (Dialog(group="STD",tab="Eco"));
  //Compressor
  //Z_Pow
  parameter Real[3] a0_pow={0.96773,0.9016,0.8946}
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  parameter Real[3] a1_SST_pow={0,-0,0}
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  parameter Real[3] a1_SDT_pow={0.0011,0.001935,0.000909}
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  parameter Real[3] a1_FreqComp_pow={0,0.000144,0.001141}
    annotation (Dialog(group="Coefficients Z_Power",tab="Compressor"));
  //Z_flow
  parameter Real[3] a0_SDT_flow={0,0,0.90}
    annotation (Dialog(group="Coefficients Z_Flow_suc",tab="Compressor"));
  parameter Real[3] a1_SDT_flow={0,0,0.00225}
    annotation (Dialog(group="Coefficients Z_Flow_suc",tab="Compressor"));
  parameter Real[3] a0_comp_flow={0,0,0}
    annotation (Dialog(group="Coefficients Z_Flow_suc",tab="Compressor"));
  parameter Real[3] a1_comp_flow={0,0,0}
    annotation (Dialog(group="Coefficients Z_Flow_suc",tab="Compressor"));
  //Condenser 05/12/2023
  parameter Real a0_ZcondDpr=0.7948
    annotation (Dialog(tab="Condenser",group="Z_Dpr coefficient"));
  parameter Real a1_ZcondDpr=1.7043
    annotation (Dialog(tab="Condenser",group="Z_Dpr coefficient"));
  parameter Boolean is_CoatingOption=false
    " true if there is coating on both coils "
    annotation (Dialog(tab="Condenser",group="Coating Option"));
  parameter Real a1_ZU_cond=1.27996e-6
    annotation (Dialog(tab="Condenser",group="Z_U coefficient"));
  parameter Real a0_ZU_cond=4.07405e-1
    annotation (Dialog(tab="Condenser",group="Z_U coefficient"));
  //Limits
  parameter Real Z_dpr_Cond_max=2.2
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_dpr_Cond_min=1
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_U_Cond_max=1.5
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Z_U_Cond_min=0.1
    annotation (Dialog(tab="Condenser",group="Limits"));
  parameter Real Zevap_max=2
    annotation (Dialog(group="Limits",tab="Evaporator"));
  parameter Real Zevap_min=0.4
    annotation (Dialog(group="Limits",tab="Evaporator"));
  parameter Real Zeco_min=0.1
    annotation (Dialog(group="STD",tab="Eco"));
  parameter Real Zeco_max=1.5
    annotation (Dialog(group="STD",tab="Eco"));
  parameter Real Zdp_max=0.8
    annotation (Dialog(group="Limits",tab="Evaporator"));
  parameter Real Zdp_min=0.4
    annotation (Dialog(group="Limits",tab="Evaporator"));
  parameter Real Zpower_max=1.12
    annotation (Dialog(group="Limits",tab="Compressor"));
  parameter Real Zpower_min=0.95
    annotation (Dialog(group="Limits",tab="Compressor"));
  parameter Real Zflow_max=1.05
    annotation (Dialog(group="Limits",tab="Compressor"));
  parameter Real Zflow_min=0.96
    annotation (Dialog(group="Limits",tab="Compressor"));
  .Modelica.Blocks.Interfaces.RealInput OAT
    annotation (Placement(transformation(extent={{-87.72283959637137,-267.7228395963714},{-68.27716040362863,-248.2771604036286}},origin={0.0,0.0},rotation=0.0)));
//Evaporator
equation
  Z_Evap_A=
    if RefrigerantType == .Workspace.Controller.Components.Types.RefrigerantSelector.R134a then
      // R134A law
      if longEvap then 1 else 0.85
    else
      // R513A law
      if longEvap then
        if LWT_C > 9.5 and load_A >= 0.7 then max(Zevap_min,min(Zevap_max,-0.0409*LWT_C + 1.2864)) else 1
      else
        if LWT_C > 9.5 and load_A >= 0.7 then max(Zevap_min,min(Zevap_max,-0.0409*LWT_C + 1.1364)) else 0.85;
  Z_Evap_B=
    if RefrigerantType == .Workspace.Controller.Components.Types.RefrigerantSelector.R134a then
      // R134A law
      if longEvap then 1 else 0.85
    else
      // R513A law
      if longEvap then
        if LWT_C > 9.5 and load_B >= 0.7 then max(Zevap_min,min(Zevap_max,-0.0409*LWT_C + 1.2864)) else 1
      else
        if LWT_C > 9.5 and load_B >= 0.7 then max(Zevap_min,min(Zevap_max,-0.0409*LWT_C + 1.1364)) else 0.85;

//Z_Evap_B   = if longEvap then 1 else  max(Zevap_min,min(Zevap_max,a1_evap*LWT + a0_evap))*SSTmaxB;
//Evaporator_Dpc
equation
  Z_Evap_dpc=max(
    Zdp_min,
    min(
      Zdp_max,
      a_dp*Re^b_dp));
//Batterie
equation
  Z_Cond_A=
    if is_CoatingOption then
      0.960*max(
        Z_U_Cond_min,
        min(
          Z_U_Cond_max,
          a1_ZU_cond*Q_flow_air_A/((nCoil_A/2)*scalingFactor_A)+a0_ZU_cond))
    else
      max(
        Z_U_Cond_min,
        min(
          Z_U_Cond_max,
          a1_ZU_cond*Q_flow_air_A/((nCoil_A/2)*scalingFactor_A)+a0_ZU_cond));
  // For -2% EER Ã  Eurovent : coeff = 0.926
  // Now for having -1% EER Ã  eurovent : coeff = 0.960
  Z_Cond_B=
    if is_CoatingOption then
      0.960*max(
        Z_U_Cond_min,
        min(
          Z_U_Cond_max,
          a1_ZU_cond*Q_flow_air_B/((nCoil_B/2)*scalingFactor_B)+a0_ZU_cond))
    else
      max(
        Z_U_Cond_min,
        min(
          Z_U_Cond_max,
          a1_ZU_cond*Q_flow_air_B/((nCoil_B/2)*scalingFactor_B)+a0_ZU_cond));
  //Z_dpr de la batterie
  Z_Cond_Dpr_A=max(
    Z_dpr_Cond_min,
    min(
      Z_dpr_Cond_max,
      a1_ZcondDpr*(mflowCoil_A/(nCoil_A/2))+a0_ZcondDpr));
  Z_Cond_Dpr_B=max(
    Z_dpr_Cond_min,
    min(
      Z_dpr_Cond_max,
      a1_ZcondDpr*(mflowCoil_B/(nCoil_B/2))+a0_ZcondDpr));
//Economiseur
equation
  Z_U_Eco_A=max(
    Zeco_min,
    min(
      Zeco_max,
      a1_eco*EcoFlux_A/EcoArea_A+a0_eco));
  Z_U_Eco_B=max(
    Zeco_min,
    min(
      Zeco_max,
      a1_eco*EcoFlux_B/EcoArea_B+a0_eco));
// Z_Power_Compressor equations with explicit parameter selection
  //Z_Power_Compressor
  //Z_Power_A = if CompressorA == .Workspace.Controller.Components.Types.CompressorSelector.NG3 then max(Zpower_min, min(Zpower_max, a1_SST_pow[n_A]*SST_A + a1_SDT_pow[n_A]*SDT_A + a1_FreqComp_pow[n_A]*FrqComp_A + a0_pow[n_A])) else 0.988*max(Zpower_min, min(Zpower_max, a1_SST_pow[n_A]*SST_A + a1_SDT_pow[n_A]*SDT_A + a1_FreqComp_pow[n_A]*FrqComp_A + a0_pow[n_A])) ;
  //Z_Power_B = if CompressorB == .Workspace.Controller.Components.Types.CompressorSelector.NG3 then max(Zpower_min, min(Zpower_max, a1_SST_pow[n_B]*SST_B + a1_SDT_pow[n_B]*SDT_B + a1_FreqComp_pow[n_B]*FrqComp_B + a0_pow[n_B])) else 0.988*max(Zpower_min, min(Zpower_max, a1_SST_pow[n_B]*SST_B + a1_SDT_pow[n_B]*SDT_B + a1_FreqComp_pow[n_B]*FrqComp_B + a0_pow[n_B]));
  load_A=FrqComp_A/FrqComp_A_Max;
  load_B=FrqComp_B/FrqComp_B_Max;
  LWT_C=LWT-273.15;
  factor_Zpower_LWT=0.008*LWT_C+0.94;
  factor_Zpower_OAT=-0.0040*OAT+1.14;
  factor_highLWT=min(
    factor_Zpower_LWT,
    1.1)*min(
    factor_Zpower_OAT,
    1.06);
  // factor_highLWT = 1;
  // on aplique un facteur degressif de 1 Ã  1.05 de 50Â°C Ã  55Â°C OAT
  factorHighOAT=max(
    1,
    min(
      1.05,
      0.01*OAT+0.5));
  Z_Power_A=
    if(LWT_C >= 9.5 and load_A >= 0.75 and OAT <= 35) then
      (
        if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG3 then
          max(
            Zpower_min,
            min(
              Zpower_max,
              factor_highLWT*((if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SST_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SST_pow[2] else a1_SST_pow[3])*SST_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SDT_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SDT_pow[2] else a1_SDT_pow[3])*SDT_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_FreqComp_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_FreqComp_pow[2] else a1_FreqComp_pow[3])*FrqComp_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a0_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a0_pow[2] else a0_pow[3]))))
        else
          0.988*max(
            Zpower_min,
            min(
              Zpower_max,
              factor_highLWT*((if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SST_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SST_pow[2] else a1_SST_pow[3])*SST_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SDT_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SDT_pow[2] else a1_SDT_pow[3])*SDT_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_FreqComp_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_FreqComp_pow[2] else a1_FreqComp_pow[3])*FrqComp_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a0_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a0_pow[2] else a0_pow[3])))))
    else
      (
        if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG3 then
          factorHighOAT*max(
            Zpower_min,
            min(
              Zpower_max,
              (if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SST_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SST_pow[2] else a1_SST_pow[3])*SST_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SDT_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SDT_pow[2] else a1_SDT_pow[3])*SDT_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_FreqComp_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_FreqComp_pow[2] else a1_FreqComp_pow[3])*FrqComp_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a0_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a0_pow[2] else a0_pow[3])))
        else
          factorHighOAT*0.988*max(
            Zpower_min,
            min(
              Zpower_max,
              (if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SST_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SST_pow[2] else a1_SST_pow[3])*SST_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SDT_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SDT_pow[2] else a1_SDT_pow[3])*SDT_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_FreqComp_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_FreqComp_pow[2] else a1_FreqComp_pow[3])*FrqComp_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a0_pow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a0_pow[2] else a0_pow[3]))));
  Z_Power_B=
    if(LWT_C >= 9.5 and load_B >= 0.75 and OAT <= 35) then
      (
        if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG3 then
          max(
            Zpower_min,
            min(
              Zpower_max,
              factor_highLWT*((if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SST_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SST_pow[2] else a1_SST_pow[3])*SST_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SDT_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SDT_pow[2] else a1_SDT_pow[3])*SDT_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_FreqComp_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_FreqComp_pow[2] else a1_FreqComp_pow[3])*FrqComp_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a0_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a0_pow[2] else a0_pow[3]))))
        else
          0.988*max(
            Zpower_min,
            min(
              Zpower_max,
              factor_highLWT*((if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SST_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SST_pow[2] else a1_SST_pow[3])*SST_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SDT_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SDT_pow[2] else a1_SDT_pow[3])*SDT_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_FreqComp_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_FreqComp_pow[2] else a1_FreqComp_pow[3])*FrqComp_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a0_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a0_pow[2] else a0_pow[3])))))
    else
      (
        if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG3 then
          factorHighOAT*max(
            Zpower_min,
            min(
              Zpower_max,
              (if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SST_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SST_pow[2] else a1_SST_pow[3])*SST_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SDT_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SDT_pow[2] else a1_SDT_pow[3])*SDT_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_FreqComp_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_FreqComp_pow[2] else a1_FreqComp_pow[3])*FrqComp_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a0_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a0_pow[2] else a0_pow[3])))
        else
          factorHighOAT*0.988*max(
            Zpower_min,
            min(
              Zpower_max,
              (if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SST_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SST_pow[2] else a1_SST_pow[3])*SST_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SDT_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SDT_pow[2] else a1_SDT_pow[3])*SDT_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_FreqComp_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_FreqComp_pow[2] else a1_FreqComp_pow[3])*FrqComp_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a0_pow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a0_pow[2] else a0_pow[3]))));
  //Z_Flow_Compressor
  Z_Flow_A=
    if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG3 then
      max(
        Zflow_min,
        min(
          Zflow_max,
          (if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SDT_flow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SDT_flow[2] else a1_SDT_flow[3])*SDT_A+(if CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a0_SDT_flow[1] elseif CompressorA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a0_SDT_flow[2] else a0_SDT_flow[3])))
    else
      1.0215;
  Z_Flow_B=
    if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG3 then
      max(
        Zflow_min,
        min(
          Zflow_max,
          (if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a1_SDT_flow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a1_SDT_flow[2] else a1_SDT_flow[3])*SDT_B+(if CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then a0_SDT_flow[1] elseif CompressorB ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then a0_SDT_flow[2] else a0_SDT_flow[3])))
    else
      1.0215;
  //ZFan
  //Zfan_A = slope_fan*frq_fan_A + intercept_fan;
  //Zfan_B = slope_fan*frq_fan_B + intercept_fan;
  Zfan_A=1.01;
  Zfan_B=1.01;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={32,50,18},
          fillPattern=FillPattern.Solid,
          extent={{-143,-143},{143,143}},
          origin={75,-5}),
        Text(
          lineColor={255,255,255},
          extent={{-143,21},{143,-21}},
          textString="CalibrationBlock",
          origin={75,-7})}));
end CalibrationBlock;
