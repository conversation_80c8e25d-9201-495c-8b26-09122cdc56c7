within Workspace.Controller.SubSystems.BaseClasses;
model LoadDistribution_DesignCapa_duplex
  parameter Real capacityDesign_moduleA_max
    "Minimum capacity design of module A (w)"
    annotation (Dialog(group="capacity design"));
  parameter Real capacityDesign_moduleB_max
    "Minimum capacity design of module B (w)"
    annotation (Dialog(group="capacity design"));
  parameter Boolean isOffModuleB;
  parameter Real ControllerSignal
    // Target capacity 
    annotation (Placement(transformation(extent={{-110.0,8.0},{-70.0,48.0}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},rotation=180,origin={-30,-38})));
  parameter Real EWT
    annotation (Placement(transformation(extent={{-106.0,-30.0},{-66.0,10.0}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-66.4}},rotation=180,origin={-30,-38})));
  parameter Real LWT
    annotation (Placement(transformation(extent={{-102.0,-66.0},{-62.0,-26.0}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-26.4}},rotation=180,origin={-30,-38})));
    
  final parameter Real SetPoint_capacity_moduleA = if isOffModuleB then ControllerSignal else ControllerSignal*((2+0.025*(EWT-LWT))/(4+0.025*(EWT-LWT)));
    
  final parameter Real SetPoint_capacity_moduleB= if isOffModuleB then 0 else ControllerSignal-SetPoint_capacity_moduleA;

    annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=true,
        extent={{-100,-100},{100,100}}),
      graphics={
        Line(
          points={{100,60},{60,60},{0,0}},
          color={0,0,127}),
        Line(
          points={{100,-60},{60,-60},{0,0}},
          color={0,0,127}),
        Line(
          points={{-100,0},{0,0}},
          color={0,0,127}),
        Rectangle(
          extent={{-40,40},{40,-40}},
          lineColor={0,0,0},
          fillColor={235,235,235},
          fillPattern=FillPattern.Solid),
        Text(
          extent={{-100,140},{100,100}},
          lineColor={28,108,200},
          textString="%name")}));
end LoadDistribution_DesignCapa_duplex;
