within StateMachine;
partial model StateMachine30XBV
  inner BOLT.Control.SteadyState.Utilities.CalculateExtraOutputs calculateExtraOutputs;
  BOLT.Control.SteadyState.StateMachine.StateMachine StateMachine(
    redeclare type ModeID=ModeID30XBV,
    redeclare record Mode=Mode30XBVBase,
    currentModeID=ModeID30XBV.CrkABCD_1,
    modes={mode_CrkABCD_1,mode_CrkABCDVIABCDEcoABCD_2,mode_CrkABCDVIABEcoABCD_3,mode_CrkABCDVICDEcoABCD_4,mode_CrkABCDVIABCDEcoAB_5,mode_CrkABCDVIABCDEcoCD_6,mode_CrkABCDVIABEcoAB_7,mode_CrkABCDVIABEcoCD_8,mode_CrkABCDVICDEcoAB_9,mode_CrkABCDVICDEcoCD_10,mode_CrkABCDVIAB_11,mode_CrkABCDVICD_12,mode_CrkABCDVIABCD_13,mode_CrkABCDEcoAB_14,mode_CrkABCDEcoCD_15,mode_CrkABCDEcoABCD_16,mode_CrkAB_17,mode_CrkABVIAB_18,mode_CrkABEcoAB_19,mode_CrkABVIABEcoAB_20})
    annotation (Placement(transformation(extent={{-109.94834955889496,74.05165044110504},{-90.05165044110504,93.94834955889496}},rotation=0.0,origin={0.0,0.0})));
  // Boolean declarations
  Boolean transitTo2;
  Boolean transitTo3;
  Boolean transitTo4;
  Boolean transitTo5;
  Boolean transitTo6;
  Boolean transitTo7;
  Boolean transitTo8;
  Boolean transitTo9;
  Boolean transitTo10;
  Boolean transitTo11;
  Boolean transitTo12;
  Boolean transitTo13;
  Boolean transitTo14;
  Boolean transitTo15;
  Boolean transitTo16;
  Boolean transitTo17;
  Boolean transitTo18;
  Boolean transitTo19;
  Boolean transitTo20;
  // Modes declarations
  CrkABCD_1 mode_CrkABCD_1(
    transitionCondition=false);
  CrkABCDVIABCDEcoABCD_2 mode_CrkABCDVIABCDEcoABCD_2(
    transitionCondition=transitTo2);
  CrkABCDVIABEcoABCD_3 mode_CrkABCDVIABEcoABCD_3(
    transitionCondition=transitTo3);
  CrkABCDVICDEcoABCD_4 mode_CrkABCDVICDEcoABCD_4(
    transitionCondition=transitTo4);
  CrkABCDVIABCDEcoAB_5 mode_CrkABCDVIABCDEcoAB_5(
    transitionCondition=transitTo5);
  CrkABCDVIABCDEcoCD_6 mode_CrkABCDVIABCDEcoCD_6(
    transitionCondition=transitTo6);
  CrkABCDVIABEcoAB_7 mode_CrkABCDVIABEcoAB_7(
    transitionCondition=transitTo7);
  CrkABCDVIABEcoCD_8 mode_CrkABCDVIABEcoCD_8(
    transitionCondition=transitTo8);
  CrkABCDVICDEcoAB_9 mode_CrkABCDVICDEcoAB_9(
    transitionCondition=transitTo9);
  CrkABCDVICDEcoCD_10 mode_CrkABCDVICDEcoCD_10(
    transitionCondition=transitTo10);
  CrkABCDVIAB_11 mode_CrkABCDVIAB_11(
    transitionCondition=transitTo11);
  CrkABCDVICD_12 mode_CrkABCDVICD_12(
    transitionCondition=transitTo12);
  CrkABCDVIABCD_13 mode_CrkABCDVIABCD_13(
    transitionCondition=transitTo13);
  CrkABCDEcoAB_14 mode_CrkABCDEcoAB_14(
    transitionCondition=transitTo14);
  CrkABCDEcoCD_15 mode_CrkABCDEcoCD_15(
    transitionCondition=transitTo15);
  CrkABCDEcoABCD_16 mode_CrkABCDEcoABCD_16(
    transitionCondition=transitTo16);
  CrkAB_17 mode_CrkAB_17(
    transitionCondition=transitTo17);
  CrkABVIAB_18 mode_CrkABVIAB_18(
    transitionCondition=transitTo18);
  CrkABEcoAB_19 mode_CrkABEcoAB_19(
    transitionCondition=transitTo19);
  CrkABVIABEcoAB_20 mode_CrkABVIABEcoAB_20(
    transitionCondition=transitTo20);
  parameter Boolean UseStateMachine
    "If true then StateMachine works normally if false, StateMachine stop and initiale current mode ID is never change"
    annotation (Dialog(group="Use Parameters"));
  // Transition condition declarations
  Boolean actuators_OnOff[12];
  Boolean CAP_LOWsat;
  Boolean ECO_ON_A;
  Boolean ECO_ON_B;
  Boolean ECO_ON_C;
  Boolean ECO_ON_D;
  Boolean VI_ON_A;
  Boolean VI_ON_B;
  Boolean VI_ON_C;
  Boolean VI_ON_D;
equation
  // Transition condition equations
  when calculateExtraOutputs then
    transitTo2=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B and ECO_ON_C and ECO_ON_D) and(VI_ON_A and VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat
      else
        false;
    transitTo3=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B and ECO_ON_C and ECO_ON_D) and(VI_ON_A and VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat
      else
        false;
    transitTo4=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B and ECO_ON_C and ECO_ON_D) and(not VI_ON_A and not VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat
      else
        false;
    transitTo5=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(VI_ON_A and VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat
      else
        false;
    transitTo6=
      if UseStateMachine then
        (not ECO_ON_A and not ECO_ON_B and ECO_ON_C and ECO_ON_D) and(VI_ON_A and VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat
      else
        false;
    transitTo7=
      if UseStateMachine then
        ((ECO_ON_A and ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(VI_ON_A and VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo8=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and ECO_ON_C and ECO_ON_D) and(VI_ON_A and VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo9=
      if UseStateMachine then
        ((ECO_ON_A and ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(not VI_ON_A and not VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo10=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and ECO_ON_C and ECO_ON_D) and(not VI_ON_A and not VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo11=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(VI_ON_A and VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo12=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(not VI_ON_A and not VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo13=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(VI_ON_A and VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo14=
      if UseStateMachine then
        ((ECO_ON_A and ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(not VI_ON_A and not VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo15=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and ECO_ON_C and ECO_ON_D) and(not VI_ON_A and not VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo16=
      if UseStateMachine then
        ((ECO_ON_A and ECO_ON_B and ECO_ON_C and ECO_ON_D) and(not VI_ON_A and not VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo17=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(not VI_ON_A and not VI_ON_B and not VI_ON_C and not VI_ON_D) and CAP_LOWsat)
      else
        false;
    transitTo18=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(VI_ON_A and VI_ON_B and not VI_ON_C and not VI_ON_D))
      else
        false;
    transitTo19=
      if UseStateMachine then
        ((ECO_ON_A and ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(not VI_ON_A and not VI_ON_B and not VI_ON_C and not VI_ON_D))
      else
        false;
    transitTo20=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(VI_ON_A and VI_ON_B and not VI_ON_C and not VI_ON_D)
      else
        false;
    
  end when;
  actuators_OnOff={
    if StateMachine.nextModeID == ModeID30XBV.CrkABCD_1 then StateMachine.modes[1].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoABCD_2 then StateMachine.modes[2].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoABCD_3 then StateMachine.modes[3].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoABCD_4 then StateMachine.modes[4].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoAB_5 then StateMachine.modes[5].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoCD_6 then StateMachine.modes[6].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoAB_7 then StateMachine.modes[7].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoCD_8 then StateMachine.modes[8].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoAB_9 then StateMachine.modes[9].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoCD_10 then StateMachine.modes[10].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIAB_11 then StateMachine.modes[11].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICD_12 then StateMachine.modes[12].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCD_13 then StateMachine.modes[13].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoAB_14 then StateMachine.modes[14].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoCD_15 then StateMachine.modes[15].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoABCD_16 then StateMachine.modes[16].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkAB_17 then StateMachine.modes[17].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABVIAB_18 then StateMachine.modes[18].CrkA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABEcoAB_19 then StateMachine.modes[19].CrkA
    else StateMachine.modes[20].CrkA,
    if StateMachine.nextModeID == ModeID30XBV.CrkABCD_1 then StateMachine.modes[1].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoABCD_2 then StateMachine.modes[2].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoABCD_3 then StateMachine.modes[3].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoABCD_4 then StateMachine.modes[4].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoAB_5 then StateMachine.modes[5].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoCD_6 then StateMachine.modes[6].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoAB_7 then StateMachine.modes[7].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoCD_8 then StateMachine.modes[8].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoAB_9 then StateMachine.modes[9].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoCD_10 then StateMachine.modes[10].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIAB_11 then StateMachine.modes[11].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICD_12 then StateMachine.modes[12].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCD_13 then StateMachine.modes[13].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoAB_14 then StateMachine.modes[14].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoCD_15 then StateMachine.modes[15].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoABCD_16 then StateMachine.modes[16].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkAB_17 then StateMachine.modes[17].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABVIAB_18 then StateMachine.modes[18].CrkB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABEcoAB_19 then StateMachine.modes[19].CrkB
    else StateMachine.modes[20].CrkB,
    if StateMachine.nextModeID == ModeID30XBV.CrkABCD_1 then StateMachine.modes[1].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoABCD_2 then StateMachine.modes[2].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoABCD_3 then StateMachine.modes[3].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoABCD_4 then StateMachine.modes[4].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoAB_5 then StateMachine.modes[5].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoCD_6 then StateMachine.modes[6].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoAB_7 then StateMachine.modes[7].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoCD_8 then StateMachine.modes[8].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoAB_9 then StateMachine.modes[9].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoCD_10 then StateMachine.modes[10].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIAB_11 then StateMachine.modes[11].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICD_12 then StateMachine.modes[12].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCD_13 then StateMachine.modes[13].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoAB_14 then StateMachine.modes[14].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoCD_15 then StateMachine.modes[15].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoABCD_16 then StateMachine.modes[16].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkAB_17 then StateMachine.modes[17].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABVIAB_18 then StateMachine.modes[18].CrkC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABEcoAB_19 then StateMachine.modes[19].CrkC
    else StateMachine.modes[20].CrkC,
    if StateMachine.nextModeID == ModeID30XBV.CrkABCD_1 then StateMachine.modes[1].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoABCD_2 then StateMachine.modes[2].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoABCD_3 then StateMachine.modes[3].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoABCD_4 then StateMachine.modes[4].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoAB_5 then StateMachine.modes[5].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoCD_6 then StateMachine.modes[6].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoAB_7 then StateMachine.modes[7].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoCD_8 then StateMachine.modes[8].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoAB_9 then StateMachine.modes[9].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoCD_10 then StateMachine.modes[10].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIAB_11 then StateMachine.modes[11].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICD_12 then StateMachine.modes[12].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCD_13 then StateMachine.modes[13].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoAB_14 then StateMachine.modes[14].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoCD_15 then StateMachine.modes[15].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoABCD_16 then StateMachine.modes[16].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkAB_17 then StateMachine.modes[17].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABVIAB_18 then StateMachine.modes[18].CrkD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABEcoAB_19 then StateMachine.modes[19].CrkD
    else StateMachine.modes[20].CrkD,
    if StateMachine.nextModeID == ModeID30XBV.CrkABCD_1 then StateMachine.modes[1].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoABCD_2 then StateMachine.modes[2].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoABCD_3 then StateMachine.modes[3].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoABCD_4 then StateMachine.modes[4].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoAB_5 then StateMachine.modes[5].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoCD_6 then StateMachine.modes[6].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoAB_7 then StateMachine.modes[7].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoCD_8 then StateMachine.modes[8].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoAB_9 then StateMachine.modes[9].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoCD_10 then StateMachine.modes[10].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIAB_11 then StateMachine.modes[11].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICD_12 then StateMachine.modes[12].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCD_13 then StateMachine.modes[13].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoAB_14 then StateMachine.modes[14].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoCD_15 then StateMachine.modes[15].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoABCD_16 then StateMachine.modes[16].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkAB_17 then StateMachine.modes[17].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABVIAB_18 then StateMachine.modes[18].EcoA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABEcoAB_19 then StateMachine.modes[19].EcoA
    else StateMachine.modes[20].EcoA,
    if StateMachine.nextModeID == ModeID30XBV.CrkABCD_1 then StateMachine.modes[1].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoABCD_2 then StateMachine.modes[2].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoABCD_3 then StateMachine.modes[3].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoABCD_4 then StateMachine.modes[4].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoAB_5 then StateMachine.modes[5].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoCD_6 then StateMachine.modes[6].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoAB_7 then StateMachine.modes[7].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoCD_8 then StateMachine.modes[8].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoAB_9 then StateMachine.modes[9].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoCD_10 then StateMachine.modes[10].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIAB_11 then StateMachine.modes[11].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICD_12 then StateMachine.modes[12].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCD_13 then StateMachine.modes[13].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoAB_14 then StateMachine.modes[14].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoCD_15 then StateMachine.modes[15].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoABCD_16 then StateMachine.modes[16].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkAB_17 then StateMachine.modes[17].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABVIAB_18 then StateMachine.modes[18].EcoB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABEcoAB_19 then StateMachine.modes[19].EcoB
    else StateMachine.modes[20].EcoB,
    if StateMachine.nextModeID == ModeID30XBV.CrkABCD_1 then StateMachine.modes[1].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoABCD_2 then StateMachine.modes[2].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoABCD_3 then StateMachine.modes[3].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoABCD_4 then StateMachine.modes[4].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoAB_5 then StateMachine.modes[5].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoCD_6 then StateMachine.modes[6].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoAB_7 then StateMachine.modes[7].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoCD_8 then StateMachine.modes[8].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoAB_9 then StateMachine.modes[9].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoCD_10 then StateMachine.modes[10].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIAB_11 then StateMachine.modes[11].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICD_12 then StateMachine.modes[12].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCD_13 then StateMachine.modes[13].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoAB_14 then StateMachine.modes[14].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoCD_15 then StateMachine.modes[15].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoABCD_16 then StateMachine.modes[16].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkAB_17 then StateMachine.modes[17].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABVIAB_18 then StateMachine.modes[18].EcoC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABEcoAB_19 then StateMachine.modes[19].EcoC
    else StateMachine.modes[20].EcoC,
    if StateMachine.nextModeID == ModeID30XBV.CrkABCD_1 then StateMachine.modes[1].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoABCD_2 then StateMachine.modes[2].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoABCD_3 then StateMachine.modes[3].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoABCD_4 then StateMachine.modes[4].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoAB_5 then StateMachine.modes[5].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoCD_6 then StateMachine.modes[6].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoAB_7 then StateMachine.modes[7].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoCD_8 then StateMachine.modes[8].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoAB_9 then StateMachine.modes[9].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoCD_10 then StateMachine.modes[10].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIAB_11 then StateMachine.modes[11].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICD_12 then StateMachine.modes[12].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCD_13 then StateMachine.modes[13].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoAB_14 then StateMachine.modes[14].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoCD_15 then StateMachine.modes[15].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoABCD_16 then StateMachine.modes[16].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkAB_17 then StateMachine.modes[17].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABVIAB_18 then StateMachine.modes[18].EcoD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABEcoAB_19 then StateMachine.modes[19].EcoD
    else StateMachine.modes[20].EcoD,
    if StateMachine.nextModeID == ModeID30XBV.CrkABCD_1 then StateMachine.modes[1].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoABCD_2 then StateMachine.modes[2].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoABCD_3 then StateMachine.modes[3].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoABCD_4 then StateMachine.modes[4].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoAB_5 then StateMachine.modes[5].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoCD_6 then StateMachine.modes[6].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoAB_7 then StateMachine.modes[7].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoCD_8 then StateMachine.modes[8].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoAB_9 then StateMachine.modes[9].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoCD_10 then StateMachine.modes[10].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIAB_11 then StateMachine.modes[11].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICD_12 then StateMachine.modes[12].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCD_13 then StateMachine.modes[13].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoAB_14 then StateMachine.modes[14].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoCD_15 then StateMachine.modes[15].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoABCD_16 then StateMachine.modes[16].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkAB_17 then StateMachine.modes[17].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABVIAB_18 then StateMachine.modes[18].ViA
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABEcoAB_19 then StateMachine.modes[19].ViA
    else StateMachine.modes[20].ViA,
    if StateMachine.nextModeID == ModeID30XBV.CrkABCD_1 then StateMachine.modes[1].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoABCD_2 then StateMachine.modes[2].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoABCD_3 then StateMachine.modes[3].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoABCD_4 then StateMachine.modes[4].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoAB_5 then StateMachine.modes[5].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoCD_6 then StateMachine.modes[6].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoAB_7 then StateMachine.modes[7].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoCD_8 then StateMachine.modes[8].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoAB_9 then StateMachine.modes[9].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoCD_10 then StateMachine.modes[10].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIAB_11 then StateMachine.modes[11].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICD_12 then StateMachine.modes[12].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCD_13 then StateMachine.modes[13].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoAB_14 then StateMachine.modes[14].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoCD_15 then StateMachine.modes[15].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoABCD_16 then StateMachine.modes[16].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkAB_17 then StateMachine.modes[17].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABVIAB_18 then StateMachine.modes[18].ViB
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABEcoAB_19 then StateMachine.modes[19].ViB
    else StateMachine.modes[20].ViB,
    if StateMachine.nextModeID == ModeID30XBV.CrkABCD_1 then StateMachine.modes[1].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoABCD_2 then StateMachine.modes[2].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoABCD_3 then StateMachine.modes[3].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoABCD_4 then StateMachine.modes[4].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoAB_5 then StateMachine.modes[5].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoCD_6 then StateMachine.modes[6].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoAB_7 then StateMachine.modes[7].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoCD_8 then StateMachine.modes[8].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoAB_9 then StateMachine.modes[9].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoCD_10 then StateMachine.modes[10].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIAB_11 then StateMachine.modes[11].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICD_12 then StateMachine.modes[12].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCD_13 then StateMachine.modes[13].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoAB_14 then StateMachine.modes[14].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoCD_15 then StateMachine.modes[15].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoABCD_16 then StateMachine.modes[16].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkAB_17 then StateMachine.modes[17].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABVIAB_18 then StateMachine.modes[18].ViC
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABEcoAB_19 then StateMachine.modes[19].ViC
    else StateMachine.modes[20].ViC,
    if StateMachine.nextModeID == ModeID30XBV.CrkABCD_1 then StateMachine.modes[1].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoABCD_2 then StateMachine.modes[2].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoABCD_3 then StateMachine.modes[3].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoABCD_4 then StateMachine.modes[4].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoAB_5 then StateMachine.modes[5].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCDEcoCD_6 then StateMachine.modes[6].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoAB_7 then StateMachine.modes[7].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABEcoCD_8 then StateMachine.modes[8].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoAB_9 then StateMachine.modes[9].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICDEcoCD_10 then StateMachine.modes[10].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIAB_11 then StateMachine.modes[11].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVICD_12 then StateMachine.modes[12].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDVIABCD_13 then StateMachine.modes[13].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoAB_14 then StateMachine.modes[14].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoCD_15 then StateMachine.modes[15].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABCDEcoABCD_16 then StateMachine.modes[16].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkAB_17 then StateMachine.modes[17].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABVIAB_18 then StateMachine.modes[18].ViD
    elseif StateMachine.nextModeID == ModeID30XBV.CrkABEcoAB_19 then StateMachine.modes[19].ViD
    else StateMachine.modes[20].ViD};
  annotation (
    Placement(
      transformation(
        extent={{-78,8},{-58,28}})),
    Placement(
      transformation(
        extent={{-68,12},{-48,32}})));
end StateMachine30XBV;
