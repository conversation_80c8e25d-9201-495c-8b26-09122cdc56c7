within Workspace.System.BaseCycle.DUPLEX.Physical;
model System_30XBV
  extends.Workspace.Controller.CL_XBV_System_duplex(
    Controller_Duplex(
      capacityDesign_moduleA_max=choiceBlock.Unit_ModuleA.Capacity_design,
      capacityDesign_moduleB_max=choiceBlock.Unit_ModuleB.Capacity_design,
      max_compressor_speed_moduleA_crkA=ModuleA.NcompAMax,
      min_compressor_speed_moduleA_crkA=ModuleA.NcompAMin,
      max_compressor_speed_moduleA_crkB=ModuleA.NcompBMax,
      min_compressor_speed_moduleA_crkB=ModuleA.NcompBMin,
      min_compressor_speed_moduleB_crkB=ModuleB.NcompBMin,
      isOffSDTmin_fan=true,
      isOffSDTmax_fan=false,
      isOffDGTmax_fan=true,
      isOffSSTmin_EXV=true,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=true,
      isOffDGTmax_EXV=true,
      isOffSSTmin_comp=true,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=true,
      max_compressor_speed_moduleB_crkA=ModuleB.NcompAMax,
      min_compressor_speed_moduleB_crkA=ModuleB.NcompAMin,
      max_compressor_speed_moduleB_crkB=ModuleB.NcompBMax,
      isOff_moduleA_crkA=ModuleA.isOffA,
      isOff_moduleA_crkB=ModuleA.isOffB,
      isOff_moduleB_crkA=ModuleB.isOffA,
      isOff_moduleB_crkB=ModuleB.isOffB,
      isOff_ecoA_moduleA=ModuleA.isOffECOA,
      isOff_ecoB_moduleA=ModuleA.isOffECOB,
      isOff_ecoA_moduleB=ModuleB.isOffECOA,
      isOff_ecoB_moduleB=ModuleB.isOffECOB,
      MaxFrequency=
        if(choiceBlock.is60HZ) then
          60
        else
          50,
      isPumpUserPresent=ModuleA.isUserPumpPresent,
      isEWTControl=
        if use_ECATBlock then
          ECAT.EvapBrineEWT_K.fixed
        else
          isEWT_Fixed),
    controllerSettings_moduleA_crkA(
      capacity_setpoint=
        if use_ECATBlock then
          ECAT.TargetCoolingCapacity_W.setPoint
        else
          TargetCapacity,
      FanFrequency_max=
        if(choiceBlock.is60HZ) then
          60
        else
          50,
      compressorFrequency_max=ModuleA.NcompAMax,
      compressorFrequency_min=ModuleA.NcompAMin,
      isHighAmbientOption=choiceBlock.isHighAmbiant,
      compressor=ModuleA.CompType_CKA,
      nbrCoils=choiceBlock.Unit_ModuleA.nCoil_CKA,
      dT_sbc_max=8,
      EWT_setpoint=
        if use_ECATBlock then
          ECAT.EvapBrineEWT_K.setPoint
        else
          EWT,
      Vflow_setpoint=
        if use_ECATBlock then
          ECAT.EvapBrineFlowRate_m3s.setPoint
        else
          Vd_coolant,
      extPressure_setpoint=
        if ModuleA.isUserPumpPresent then
          ModuleA.pDisp
        else
          source.p_set,
      OAT_sdt_offset=ModuleA.OAT,
      is60HZ=choiceBlock.is60HZ),
    controllerSettings_moduleA_crkB(
      capacity_setpoint=
        if use_ECATBlock then
          ECAT.TargetCoolingCapacity_W.setPoint
        else
          TargetCapacity,
      compressorFrequency_max=ModuleA.NcompBMax,
      compressorFrequency_min=ModuleA.NcompBMin,
      isHighAmbientOption=choiceBlock.isHighAmbiant,
      FanFrequency_max=
        if(choiceBlock.is60HZ) then
          60
        else
          50,
      compressor=ModuleA.CompType_CKB,
      nbrCoils=choiceBlock.Unit_ModuleA.nCoil_CKB,
      dT_sbc_max=8,
      EWT_setpoint=
        if use_ECATBlock then
          ECAT.EvapBrineEWT_K.setPoint
        else
          EWT,
      Vflow_setpoint=
        if use_ECATBlock then
          ECAT.EvapBrineFlowRate_m3s.setPoint
        else
          Vd_coolant,
      extPressure_setpoint=
        if ModuleA.isUserPumpPresent then
          ModuleA.pDisp
        else
          source.p_set,
      OAT_sdt_offset=ModuleA.OAT,
      is60HZ=choiceBlock.is60HZ),
    controllerSettings_moduleB_crkA(
      capacity_setpoint=
        if use_ECATBlock then
          ECAT.TargetCoolingCapacity_W.setPoint
        else
          TargetCapacity,
      FanFrequency_max=
        if(choiceBlock.is60HZ) then
          60
        else
          50,
      compressorFrequency_max=ModuleB.NcompAMax,
      compressorFrequency_min=ModuleB.NcompAMin,
      isHighAmbientOption=choiceBlock.isHighAmbiant,
      compressor=ModuleB.CompType_CKA,
      nbrCoils=choiceBlock.Unit_ModuleB.nCoil_CKA,
      dT_sbc_max=8,
      EWT_setpoint=
        if use_ECATBlock then
          ECAT.EvapBrineEWT_K.setPoint
        else
          EWT,
      Vflow_setpoint=
        if use_ECATBlock then
          ECAT.EvapBrineFlowRate_m3s.setPoint
        else
          Vd_coolant,
      extPressure_setpoint=
        if ModuleB.isUserPumpPresent then
          ModuleA.pDisp
        else
          source.p_set,
      OAT_sdt_offset=ModuleB.OAT,
      is60HZ=choiceBlock.is60HZ),
    controllerSettings_moduleB_crkB(
      capacity_setpoint=
        if use_ECATBlock then
          ECAT.TargetCoolingCapacity_W.setPoint
        else
          TargetCapacity,
      FanFrequency_max=
        if(choiceBlock.is60HZ) then
          60
        else
          50,
      compressorFrequency_max=ModuleB.NcompBMax,
      compressorFrequency_min=ModuleB.NcompBMin,
      isHighAmbientOption=choiceBlock.isHighAmbiant,
      compressor=ModuleB.CompType_CKB,
      nbrCoils=choiceBlock.Unit_ModuleB.nCoil_CKB,
      dT_sbc_max=8,
      EWT_setpoint=
        if use_ECATBlock then
          ECAT.EvapBrineEWT_K.setPoint
        else
          EWT,
      Vflow_setpoint=
        if use_ECATBlock then
          ECAT.EvapBrineFlowRate_m3s.setPoint
        else
          Vd_coolant,
      extPressure_setpoint=
        if ModuleB.isUserPumpPresent then
          ModuleA.pDisp
        else
          source.p_set,
      OAT_sdt_offset=ModuleB.OAT,
      is60HZ=choiceBlock.is60HZ),
    UseStateMachine=true);
  .Workspace.Auxiliary.OptionBlock.ChoiceBlock_XBV_Duplex.ChoiceBlock choiceBlock
    annotation (Placement(transformation(extent={{-53.9696862911983,70.0303137088017},{-26.0303137088017,97.9696862911983}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.BaseCycle.Equipement_physical ModuleA(
    Vi_B=Vi_moduleA_crkB,
    NcompAMax=
      if(choiceBlock.isCoatingOption) then
        choiceBlock.Unit_ModuleA.NcompMax_coating_CKA
      else
        choiceBlock.Unit_ModuleA.NcompMax_CKA,
    NcompBMax=
      if(choiceBlock.isCoatingOption) then
        choiceBlock.Unit_ModuleA.NcompMax_coating_CKB
      else
        choiceBlock.Unit_ModuleA.NcompMax_CKB,
    NcompAMin=choiceBlock.Unit_ModuleA.NcompMin_CKA,
    NcompBMin=choiceBlock.Unit_ModuleA.NcompMin_CKB,
    Selector_comp_crkB=choiceBlock.Unit_ModuleA.selector_Comp_CKB,
    voltage_crkA=choiceBlock.Unit_ModuleA.CompVoltage_CKA,
    voltage_crkB=choiceBlock.Unit_ModuleA.CompVoltage_CKB,
    max_fan_rpm=
      if(choiceBlock.is60HZ) then
        1140
      else
        950,
    max_motor_frequency=
      if(choiceBlock.is60HZ) then
        60
      else
        50,
    limit_max_fan_rpm=
      if(choiceBlock.is60HZ) then
        1140
      else
        950,
    n_plate_crkA=choiceBlock.Unit_ModuleA.eco_nPlate_CKA,
    n_plate_crkB=choiceBlock.Unit_ModuleA.eco_nPlate_CKB,
    Eco_Geo_CKA=choiceBlock.Unit_ModuleA.Eco_Geo_CKA,
    Eco_Geo_CKB=choiceBlock.Unit_ModuleA.Eco_Geo_CKB,
    EXV_main_A=choiceBlock.Unit_ModuleA.EXV_main_A,
    EXV_main_B=choiceBlock.Unit_ModuleA.EXV_main_B,
    EXV_eco_A=choiceBlock.Unit_ModuleA.EXV_eco_A,
    EXV_eco_B=choiceBlock.Unit_ModuleA.EXV_eco_B,
    n_coilsB=choiceBlock.Unit_ModuleA.nCoil_CKB,
    n_coilsA=choiceBlock.Unit_ModuleA.nCoil_CKA,
    evap_selector_tube=choiceBlock.Unit_ModuleA.evap_selector_tube,
    length_tube_evap_crkA=choiceBlock.Unit_ModuleA.evap_length_tube_CKA,
    length_tube_evap_crkB=choiceBlock.Unit_ModuleA.evap_length_tube_CKB,
    diameter_in_shell_evap=choiceBlock.Unit_ModuleA.evap_diameter_in_shell,
    n_tubes_passe1_evap=choiceBlock.Unit_ModuleA.evap_n_tubes_pass1,
    n_tubes_passe2_evap=choiceBlock.Unit_ModuleA.evap_n_tubes_pass2,
    Di_oil_crkA=choiceBlock.Unit_ModuleA.OilSepDiameter_CKA,
    Di_oil_crkB=choiceBlock.Unit_ModuleA.OilSepDiameter_CKB,
    length_oil_crkA=choiceBlock.Unit_ModuleA.OilSepLength_CKA,
    length_oil_crkB=choiceBlock.Unit_ModuleA.OilSepLength_CKB,
    dp_ref_oil_crkA=choiceBlock.Unit_ModuleA.Oil_sepA_DP,
    dp_ref_oil_crkB=choiceBlock.Unit_ModuleA.Oil_sepB_DP,
    m_flow_ref_oil_crkA=choiceBlock.Unit_ModuleA.Oil_sepA_MF,
    m_flow_ref_oil_crkB=choiceBlock.Unit_ModuleA.Oil_sepB_MF,
    dp_ref_SL_crkA=choiceBlock.Unit_ModuleA.Suc_lineA_DP,
    dp_ref_SL_crkB=choiceBlock.Unit_ModuleA.Suc_lineB_DP,
    m_flow_ref_SL_crkA=choiceBlock.Unit_ModuleA.Suc_lineA_MF,
    m_flow_ref_SL_crkB=choiceBlock.Unit_ModuleA.Suc_lineB_MF,
    dp_ref_DL_crkA=choiceBlock.Unit_ModuleA.Dis_lineA_DP,
    dp_ref_DL_crkB=choiceBlock.Unit_ModuleA.Dis_lineB_DP,
    m_flow_ref_DL_crkA=choiceBlock.Unit_ModuleA.Dis_lineA_MF,
    m_flow_ref_DL_crkB=choiceBlock.Unit_ModuleA.Dis_lineB_MF,
    isOffECOA=isEcoOff_moduleA_crkA,
    isOffECOB=isEcoOff_moduleA_crkB,
    sinkBrine_init=(source.T_set+sink.T_set)/2,
    use_bf=use_bf,
    use_en=Use_EN14511,
    Selector_comp_crkA=choiceBlock.Unit_ModuleA.selector_Comp_CKA,
    isOffA=isOff_moduleA_crkA,
    isOffB=isOff_moduleA_crkB,
    OAT=
      if use_ECATBlock then
        ECAT.AmbientAirDBTemp_K.setPoint
      else
        OAT,
    is_fixedSpeed=choiceBlock.FAN_SPEED,
    CoolantMedium=ECAT.EvapBrineType_nd,
    BrineConcentration=ECAT.EvapBrineConcentration_nd.setPoint,
    capacity_design=choiceBlock.Unit_ModuleA.Capacity_design,
    use_Calib=use_Calib,
    CompType_CKA=choiceBlock.Unit_ModuleA.CompType_CKA,
    CompType_CKB=choiceBlock.Unit_ModuleA.CompType_CKB,
    eco_DportH_a_A=choiceBlock.Unit_ModuleA.eco_DportH_a_A,
    eco_DportH_b_A=choiceBlock.Unit_ModuleA.eco_DportH_b_A,
    eco_DportL_a_A=choiceBlock.Unit_ModuleA.eco_DportL_a_A,
    eco_DportL_b_A=choiceBlock.Unit_ModuleA.eco_DportL_b_A,
    eco_DportH_a_B=choiceBlock.Unit_ModuleA.eco_DportH_a_A,
    eco_DportH_b_B=choiceBlock.Unit_ModuleA.eco_DportH_b_A,
    eco_DportL_a_B=choiceBlock.Unit_ModuleA.eco_DportL_a_A,
    eco_DportL_b_B=choiceBlock.Unit_ModuleA.eco_DportL_b_A,
    EvapFoulingFactor=ECAT.EvapFoulingFactor_m2KW.setPoint,
    n_passes_evap=1,
    selector_VFDA_CKA=choiceBlock.VFD_Type_moduleA[1],
    selector_VFDA_CKB=choiceBlock.VFD_Type_moduleA[2],
    Vi_A=Vi_moduleA_crkA,
    isCoating=choiceBlock.isCoatingOption,
    relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    diameter_nozzle=choiceBlock.Unit_ModuleA.evap_diameter_nozzle,
    longEvap=choiceBlock.Unit_ModuleA.longEvap,
    LWT=
      if use_ECATBlock then
        ECAT.EvapBrineLWT_K.setPoint
      else
        LWT,
    sourceBrine_init=source.T_set,
    fractionB_start=0.04,
    altitude=ECAT.Altitude_m.setPoint,
    isUserPumpPresent=choiceBlock.is_Pump,
    selector_PumpUser=choiceBlock.Unit_ModuleA.selector_pump_type,
    efficieny_userPumpFictitious=0.9,
    pDisp=
      if use_ECATBlock then
        (ECAT.ExternalSystemPressureDrop_Pa.setPoint)/2
      else
        pDisp_moduleA,
    EWT_init=
      if use_ECATBlock then
        ECAT.EvapBrineEWT_K.setPoint
      else
        EWT,
    mdot_start=ModuleA.capacity_design/(((4180*(ModuleA.EWT_init-ModuleA.LWT)))))
    annotation (Placement(transformation(extent={{-54.86273949010395,-17.137260509896052},{-81.13726050989605,9.137260509896052}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.BaseCycle.Equipement_physical ModuleB(
    Selector_comp_crkA=choiceBlock.Unit_ModuleB.selector_Comp_CKA,
    use_en=Use_EN14511,
    use_bf=use_bf,
    sinkBrine_init=sink.T_set,
    isOffECOB=isEcoOff_moduleB_crkB,
    isOffECOA=isEcoOff_moduleB_crkA,
    m_flow_ref_DL_crkB=choiceBlock.Unit_ModuleB.Dis_lineB_MF,
    m_flow_ref_DL_crkA=choiceBlock.Unit_ModuleB.Dis_lineA_MF,
    dp_ref_DL_crkB=choiceBlock.Unit_ModuleB.Dis_lineB_DP,
    dp_ref_DL_crkA=choiceBlock.Unit_ModuleB.Dis_lineA_DP,
    m_flow_ref_SL_crkB=choiceBlock.Unit_ModuleB.Suc_lineB_MF,
    m_flow_ref_SL_crkA=choiceBlock.Unit_ModuleB.Suc_lineA_MF,
    dp_ref_SL_crkB=choiceBlock.Unit_ModuleB.Suc_lineB_DP,
    dp_ref_SL_crkA=choiceBlock.Unit_ModuleB.Suc_lineA_DP,
    m_flow_ref_oil_crkB=choiceBlock.Unit_ModuleB.Oil_sepB_MF,
    m_flow_ref_oil_crkA=choiceBlock.Unit_ModuleB.Oil_sepA_MF,
    dp_ref_oil_crkB=choiceBlock.Unit_ModuleB.Oil_sepB_DP,
    dp_ref_oil_crkA=choiceBlock.Unit_ModuleB.Oil_sepA_DP,
    length_oil_crkB=choiceBlock.Unit_ModuleB.OilSepLength_CKB,
    length_oil_crkA=choiceBlock.Unit_ModuleB.OilSepLength_CKA,
    Di_oil_crkB=choiceBlock.Unit_ModuleB.OilSepDiameter_CKB,
    Di_oil_crkA=choiceBlock.Unit_ModuleB.OilSepDiameter_CKA,
    n_tubes_passe2_evap=choiceBlock.Unit_ModuleB.evap_n_tubes_pass2,
    n_tubes_passe1_evap=choiceBlock.Unit_ModuleB.evap_n_tubes_pass1,
    diameter_in_shell_evap=choiceBlock.Unit_ModuleB.evap_diameter_in_shell,
    length_tube_evap_crkB=choiceBlock.Unit_ModuleB.evap_length_tube_CKB,
    length_tube_evap_crkA=choiceBlock.Unit_ModuleB.evap_length_tube_CKA,
    evap_selector_tube=choiceBlock.Unit_ModuleB.evap_selector_tube,
    n_coilsA=choiceBlock.Unit_ModuleB.nCoil_CKA,
    n_coilsB=choiceBlock.Unit_ModuleB.nCoil_CKB,
    EXV_eco_B=choiceBlock.Unit_ModuleB.EXV_eco_B,
    EXV_eco_A=choiceBlock.Unit_ModuleB.EXV_eco_A,
    EXV_main_B=choiceBlock.Unit_ModuleB.EXV_main_B,
    EXV_main_A=choiceBlock.Unit_ModuleB.EXV_main_A,
    Eco_Geo_CKB=choiceBlock.Unit_ModuleB.Eco_Geo_CKB,
    Eco_Geo_CKA=choiceBlock.Unit_ModuleB.Eco_Geo_CKA,
    n_plate_crkB=choiceBlock.Unit_ModuleB.eco_nPlate_CKB,
    n_plate_crkA=choiceBlock.Unit_ModuleB.eco_nPlate_CKA,
    limit_max_fan_rpm=
      if(choiceBlock.is60HZ) then
        1140
      else
        950,
    max_motor_frequency=
      if(choiceBlock.is60HZ) then
        60
      else
        50,
    max_fan_rpm=
      if(choiceBlock.is60HZ) then
        1140
      else
        950,
    voltage_crkB=choiceBlock.Unit_ModuleB.CompVoltage_CKB,
    voltage_crkA=choiceBlock.Unit_ModuleB.CompVoltage_CKA,
    Selector_comp_crkB=choiceBlock.Unit_ModuleB.selector_Comp_CKB,
    NcompBMin=choiceBlock.Unit_ModuleB.NcompMin_CKB,
    NcompAMin=choiceBlock.Unit_ModuleB.NcompMin_CKA,
    NcompBMax=
      if(choiceBlock.isCoatingOption) then
        choiceBlock.Unit_ModuleB.NcompMax_coating_CKB
      else
        choiceBlock.Unit_ModuleB.NcompMax_CKB,
    NcompAMax=
      if(choiceBlock.isCoatingOption) then
        choiceBlock.Unit_ModuleB.NcompMax_coating_CKA
      else
        choiceBlock.Unit_ModuleB.NcompMax_CKA,
    Vi_B=Vi_moduleB_crkB,
    isOffA=isOff_moduleB_crkA,
    isOffB=isOff_moduleB_crkB,
    OAT=
      if use_ECATBlock then
        ECAT.AmbientAirDBTemp_K.setPoint
      else
        OAT,
    is_fixedSpeed=choiceBlock.FAN_SPEED,
    BrineConcentration=ECAT.EvapBrineConcentration_nd.setPoint,
    CoolantMedium=ECAT.EvapBrineType_nd,
    capacity_design=choiceBlock.Unit_ModuleB.Capacity_design,
    use_Calib=use_Calib,
    CompType_CKA=choiceBlock.Unit_ModuleB.CompType_CKA,
    CompType_CKB=choiceBlock.Unit_ModuleB.CompType_CKB,
    eco_DportH_a_A=choiceBlock.Unit_ModuleB.eco_DportH_a_B,
    eco_DportH_b_A=choiceBlock.Unit_ModuleB.eco_DportH_b_B,
    eco_DportL_a_A=choiceBlock.Unit_ModuleB.eco_DportL_a_B,
    eco_DportL_b_A=choiceBlock.Unit_ModuleB.eco_DportL_b_B,
    eco_DportH_a_B=choiceBlock.Unit_ModuleB.eco_DportH_a_B,
    eco_DportH_b_B=choiceBlock.Unit_ModuleB.eco_DportH_b_B,
    eco_DportL_a_B=choiceBlock.Unit_ModuleB.eco_DportL_a_B,
    eco_DportL_b_B=choiceBlock.Unit_ModuleB.eco_DportL_b_B,
    EvapFoulingFactor=ECAT.EvapFoulingFactor_m2KW.setPoint,
    n_passes_evap=1,
    selector_VFDA_CKA=choiceBlock.VFD_Type_moduleB[1],
    selector_VFDA_CKB=choiceBlock.VFD_Type_moduleB[2],
    Vi_A=Vi_moduleB_crkA,
    isCoating=choiceBlock.isCoatingOption,
    relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    diameter_nozzle=choiceBlock.Unit_ModuleB.evap_diameter_nozzle,
    longEvap=choiceBlock.Unit_ModuleB.longEvap,
    LWT=
      if use_ECATBlock then
        ECAT.EvapBrineLWT_K.setPoint
      else
        LWT,
    sourceBrine_init=ModuleA.sinkBrine_init,
    fractionB_start=0.04,
    altitude=ECAT.Altitude_m.setPoint,
    c_load_bf_cap=ModuleA.c_load_bf_cap,
    c_const_bf_cap=ModuleA.c_const_bf_cap,
    c_load2_bf_cap=ModuleA.c_load2_bf_cap,
    c_load2_bf_pow=ModuleA.c_load2_bf_pow,
    c_load_bf_pow=ModuleA.c_load_bf_pow,
    c_const_bf_pow=ModuleA.c_const_bf_pow,
    bf_cap_max=ModuleA.bf_cap_max,
    bf_cap_min=ModuleA.bf_cap_min,
    bf_pow_max=ModuleA.bf_pow_max,
    bf_pow_min=ModuleA.bf_pow_min,
    isUserPumpPresent=choiceBlock.is_Pump,
    efficieny_userPumpFictitious=0.9,
    selector_PumpUser=choiceBlock.Unit_ModuleB.selector_pump_type,
    pDisp=
      if use_ECATBlock then
        (ECAT.ExternalSystemPressureDrop_Pa.setPoint)/2
      else
        pDisp_moduleB,
    EWT_init=
      if use_ECATBlock then
        ECAT.EvapBrineEWT_K.setPoint
      else
        EWT,
    mdot_start=ModuleB.capacity_design/(((4180*(ModuleB.EWT_init-ModuleB.LWT)))),
    c_load2_bf_pow_opt17A=ModuleA.c_load2_bf_pow_opt17A,
    c_load_bf_pow_opt17A=ModuleA.c_load_bf_pow_opt17A,
    c_const_bf_pow_opt17A=ModuleA.c_const_bf_pow_opt17A,
    bf_pow_max_opt17A=ModuleA.bf_pow_max_opt17A,
    bf_pow_min_opt17A=ModuleA.bf_pow_min_opt17A)
    annotation (Placement(transformation(extent={{17.431917342627102,-14.568082657372898},{42.5680826573729,10.568082657372898}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source source(
    p_fixed=true,
    Vd_fixed=
      if use_ECATBlock then
        ECAT.EvapBrineFlowRate_m3s.fixed and not ModuleA.isUserPumpPresent
      else
        isVd_Fixed and not ModuleA.isUserPumpPresent,
    T_set=
      if use_ECATBlock then
        ECAT.EvapBrineEWT_K.setPoint
      else
        EWT,
    X=ECAT.EvapBrineConcentration_nd.setPoint,
    CoolantMedium=ECAT.EvapBrineType_nd,
    Vd_set=
      if use_ECATBlock then
        ECAT.EvapBrineFlowRate_m3s.setPoint
      else
        Vd_coolant,
    T_fixed=
      if use_ECATBlock then
        ECAT.EvapBrineEWT_K.fixed and(not ModuleA.isUserPumpPresent or not ECAT.EvapBrineLWT_K.fixed)
      else
        isEWT_Fixed and(not ModuleA.isUserPumpPresent or not ECAT.EvapBrineLWT_K.fixed))
    annotation (Placement(transformation(extent={{-142.58286358329377,-8.5828635832938},{-125.41713641670623,8.5828635832938}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Sink sink(
    T_set=
      if use_ECATBlock then
        ECAT.EvapBrineLWT_K.setPoint
      else
        LWT,
    p_fixed=ModuleA.isUserPumpPresent,
    T_fixed=
      if use_ECATBlock then
        ECAT.EvapBrineLWT_K.fixed
      else
        isLWT_Fixed,
    X=ECAT.EvapBrineConcentration_nd.setPoint,
    CoolantMedium=ECAT.EvapBrineType_nd,
    p_set=source.p_set)
    annotation (Placement(transformation(extent={{105.9180849963137,-11.918084996313684},{86.0819150036863,7.918084996313684}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Node node(
    T_set=ModuleA.sinkBrine_init,
    T_fixed=false,
    X=ECAT.EvapBrineConcentration_nd.setPoint,
    CoolantMedium=ECAT.EvapBrineType_nd)
    annotation (Placement(transformation(extent={{-26.546017779007418,-38.546017779007414},{-13.453982220992582,-25.453982220992586}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature OAT=35+273.15
    annotation (Dialog(group="Boundary Conditions"));
  parameter.Modelica.SIunits.Temperature EWT=12+273.15
    annotation (Dialog(group="Boundary Conditions"));
  parameter.Modelica.SIunits.Temperature LWT=7+273.15
    annotation (Dialog(group="Boundary Conditions"));
  parameter Real TargetCapacity=2000000
    annotation (Dialog(group="Boundary Conditions"));
  parameter Real Vd_coolant=0.015
    annotation (Dialog(group="Boundary Conditions"));
  parameter Real pDisp_moduleA=50000
    "Available static pressure module A for user if user pump is present"
    annotation (Dialog(group="Boundary conditions (Use only if 'use_ECATBlock' is FALSE)"));
  parameter Real pDisp_moduleB=50000
    "Available static pressure module A for user if user pump is present"
    annotation (Dialog(group="Boundary conditions (Use only if 'use_ECATBlock' is FALSE)"));
  parameter Boolean isEWT_Fixed=true
    annotation (Dialog(group="Boundary Conditions"));
  parameter Boolean isLWT_Fixed=true
    annotation (Dialog(group="Boundary Conditions"));
  parameter Boolean isVd_Fixed=true
    annotation (Dialog(group="Boundary Conditions"));
  parameter Boolean isOff_moduleA_crkA=not StateMachine.currentMode.CrkA
    "set true to turn off circuit A"
    annotation (Dialog(tab="isOff"));
  parameter Boolean isOff_moduleA_crkB=not StateMachine.currentMode.CrkB
    "set true to turn off circuit B"
    annotation (Dialog(tab="isOff"));
  parameter Boolean isOff_moduleB_crkA=not StateMachine.currentMode.CrkC
    "set true to turn off circuit A"
    annotation (Dialog(tab="isOff"));
  parameter Boolean isOff_moduleB_crkB=not StateMachine.currentMode.CrkD
    "set true to turn off circuit B"
    annotation (Dialog(tab="isOff"));
  parameter Boolean isEcoOff_moduleA_crkA=not StateMachine.currentMode.EcoA
    "set true to turn off eco line in circuit A"
    annotation (Dialog(tab="isOff"));
  parameter Boolean isEcoOff_moduleA_crkB=not StateMachine.currentMode.EcoB
    "set true to turn off eco line in circuit B"
    annotation (Dialog(tab="isOff"));
  parameter Boolean isEcoOff_moduleB_crkA=not StateMachine.currentMode.EcoC
    "set true to turn off eco line in circuit A"
    annotation (Dialog(tab="isOff"));
  parameter Boolean isEcoOff_moduleB_crkB=not StateMachine.currentMode.EcoD
    "set true to turn off eco line in circuit B"
    annotation (Dialog(tab="isOff"));
  parameter Boolean Vi_moduleA_crkA=StateMachine.currentMode.ViA
    "set true to turn on circuit A VI"
    annotation (Dialog(tab="isOff"));
  parameter Boolean Vi_moduleA_crkB=StateMachine.currentMode.ViB
    "set true to turn off circuit B VI"
    annotation (Dialog(tab="isOff"));
  parameter Boolean Vi_moduleB_crkA=StateMachine.currentMode.ViC
    "set true to turn on circuit A VI"
    annotation (Dialog(tab="isOff"));
  parameter Boolean Vi_moduleB_crkB=StateMachine.currentMode.ViD
    "set true to turn off circuit B VI"
    annotation (Dialog(tab="isOff"));
  parameter Real ECO_ON_inter=3.517
    "Value of the OAT max to turn on the ECO at 273.15 K for LWT"
    annotation (Dialog(tab="isOff"));
  parameter Real ECO_ON_slope=0.827
    "Slope for the boundary to turn off the ECO according to the OAT and the LWT"
    annotation (Dialog(tab="isOff"));
  parameter Integer Vd_flow_moduleB_crkA = if isOff_moduleB_crkA then 0 else 1
     annotation (Dialog(tab="isOff"));
  parameter Integer Vd_flow_moduleB_crkB = if isOff_moduleB_crkB then 0 else 1
     annotation (Dialog(tab="isOff"));
  parameter Boolean use_bf=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean Use_EN14511=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_Calib=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_ECATBlock=true
    annotation (Dialog(group="Use Parameters"));
  import SI=Modelica.SIunits;
  import Modelica.SIunits.Conversions.*;
  .Workspace.Auxiliary.ECAT_Block.ECAT30XBVBase_Duplex ECAT(
    RefrigerantCharge_kg(
      fixed={false,false,false,false}),
    CondCoilAirPressDrop_Pa(
      value={ModuleA.condAirA.summary.dp_air,ModuleA.condAirB.summary.dp_air,ModuleB.condAirA.summary.dp_air,ModuleB.condAirB.summary.dp_air}),
    RefrigerantSST_K(
      value={ModuleA.nodeCpinA.summary.Tsat,ModuleA.nodeCpinB.summary.Tsat,ModuleB.nodeCpinA.summary.Tsat,ModuleB.nodeCpinB.summary.Tsat}),
    RefrigerantSDT_K(
      value={ModuleA.nodeCpoutA.summary.Tsat,ModuleA.nodeCpoutB.summary.Tsat,ModuleB.nodeCpoutA.summary.Tsat,ModuleB.nodeCpoutB.summary.Tsat}),
    RefrigerantSET_K(
      value={ModuleA.nodeevapoutA.summary.Tsat,ModuleA.nodeevapoutB.summary.Tsat,ModuleB.nodeevapoutA.summary.Tsat,ModuleB.nodeevapoutB.summary.Tsat}),
    RefrigerantSCT_K(
      value={ModuleA.nodecondAirinA.summary.Tsat,ModuleA.nodecondAirinB.summary.Tsat,ModuleB.nodecondAirinA.summary.Tsat,ModuleB.nodecondAirinB.summary.Tsat}),
    RefrigerantDGT_K(
      value={ModuleA.nodeCpoutA.summary.T,ModuleA.nodeCpoutB.summary.T,ModuleB.nodeCpoutA.summary.T,ModuleB.nodeCpoutB.summary.T}),
    SuctionSuperheat_K(
      value={ModuleA.nodeCpinA.summary.dTsh,ModuleA.nodeCpinB.summary.dTsh,ModuleB.nodeCpinA.summary.dTsh,ModuleB.nodeCpinB.summary.dTsh}),
    CondSubcooling_K(
      value={ModuleA.nodecondAiroutA.summary.dTsh,ModuleA.nodecondAiroutB.summary.dTsh,ModuleB.nodecondAiroutA.summary.dTsh,ModuleB.nodecondAiroutB.summary.dTsh}),
    DischargeSuperheat_K(
      value={ModuleA.nodeCpoutA.summary.dTsh,ModuleA.nodeCpoutB.summary.dTsh,ModuleB.nodeCpoutA.summary.dTsh,ModuleB.nodeCpoutB.summary.dTsh}),
    CondFanAirflowRate_m3s(
      value={ModuleA.sourceAirA.summary.Vd_flow * ModuleA.n_coilsA,ModuleA.sourceAirB.summary.Vd_flow * ModuleA.n_coilsB,ModuleB.sourceAirA.summary.Vd_flow * ModuleB.n_coilsA * Vd_flow_moduleB_crkA,ModuleB.sourceAirB.summary.Vd_flow * ModuleB.n_coilsB * Vd_flow_moduleB_crkB}),
    CompressorPower_W(
      value={ModuleA.CompressorA.summary.P_compression,ModuleA.CompressorB.summary.P_compression,ModuleB.CompressorA.summary.P_compression,ModuleB.CompressorB.summary.P_compression}),
    Altitude_m(
      setPoint=0),
    FanPower_W(
      value={ModuleA.motorA.computeStreamsAggregates.pow,ModuleA.motorB.computeStreamsAggregates.pow,ModuleB.motorA.computeStreamsAggregates.pow,ModuleB.motorB.computeStreamsAggregates.pow}),
    AmbientAirDBTemp_K(
      setPoint=35+273.15,
      value=ModuleA.sourceAirA.Tdb),
    AmbientAirRH_nd(
      setPoint=0.6,
      value=ModuleA.sourceAirA.RH),
    CondBrineFlowRate_m3s(
      value=0),
    CondFoulingFactor_m2KW(
      setPoint=0),
    EvapBrineConcentration_nd(
      setPoint=0.2),
    EvapBrineLWT_K(
      value=sink.summary.T,
      setPoint=7+273.15),
    EvapBrineEWT_K(
      setPoint=20+273.15,
      value=
        if ECAT.EvapBrineEWT_K.fixed then
          source.summary.T
        else
          ModuleA.businessFactors.bf_cap_interp*source.summary.T+(1-ModuleA.businessFactors.bf_cap_interp)*sink.summary.T),
    EvapBrineFlowRate_m3s(
      value=
        if ECAT.EvapBrineFlowRate_m3s.fixed then
          source.summary.Vd
        else
          source.summary.Vd*ModuleA.businessFactors.bf_cap_interp,
      setPoint=0.015),
    TotalRefrigerantCharge_kg(
      value=ModuleA.systemVariables.summary.mRef[1]+ModuleA.systemVariables.summary.mRef[2]+ModuleB.systemVariables.summary.mRef[1]+ModuleB.systemVariables.summary.mRef[2]),
    EvapPumpSpeed_rpm(
      value=summary_system.Pump_frequency_A,
      fixed=false),
    TotalOilCharge_kg(
      value=0),
    PubUnitPower_W(
      value=ModuleA.controlledPower+ModuleB.controlledPower),
    PubCoolingCapacity_W(
      value=ModuleA.controlledCapacity+ModuleB.controlledCapacity),
    PubHeatingCapacity_W(
      value=1),
    TotalCompressorPower_W(
      value=ModuleA.CompressorA.summary.P_compression+ModuleA.CompressorB.summary.P_compression+ModuleB.CompressorA.summary.P_compression+ModuleB.CompressorB.summary.P_compression),
    CondPumpPower_W(
      value=-1),
    TotalFanPower_W(
      value=sum(
        ECAT.FanPower_W.value)),
    EvapPumpPower_W(
      value=summary_system.Pump_power_A+summary_system.Pump_power_B),
    CondPumpSpeed_rpm(
      value=-1),
    EvapBrineIntPressDrop_Pa(
      value=ModuleA.evaporator.summary.dp_coolant+ModuleB.evaporator.summary.dp_coolant),
    EvapPumpTotalHead_m(
      value=-1),
    EvapBrineDensity_kgm3(
      value=1/(sink.summary.v)),
    CondBrineIntPressDrop_Pa(
      value=-1),
    CondPumpTotalHead_m(
      value=-1),
    CondBrineDensity_kgm3(
      value=-1),
    EvapBrineVelocity_mps(
      value=-1),
    CondBrineVelocity_mps(
      value=-1),
    EN14511PumpPowerCorrectionWithPump_W(
      value=ModuleA.en14511.M_pump+ModuleB.en14511.M_pump),
    EN14511PumpPowerCorrectionWithoutPump_W(
      value=ModuleA.en14511.M+ModuleB.en14511.M),
    ExternalSystemKa_kPas2L2(
      value=-1,
      fixed=false),
    HeatingAmbientAirWBTemp_K(
      value=-1),
    CondCoilHeatingCapacity_W(
      value={ModuleA.condAirA.summary.Q_flow_air,ModuleA.condAirB.summary.Q_flow_air,ModuleB.condAirA.summary.Q_flow_air,ModuleB.condAirB.summary.Q_flow_air}),
    TargetCoolingCapacity_W(
      setPoint=3000000),
    EvapFoulingFactor_m2KW(
      setPoint=0,
      value=ModuleA.evaporator.R_foul),
    CoolantFreezingTemp_K(
      value=ModuleA.FreezTemp),
    nbrCircuit=4,
    CompressorFrequency_Hz(
      value={ModuleA.CompressorA.summary.Ncomp,ModuleA.CompressorB.summary.Ncomp,ModuleB.CompressorA.summary.Ncomp,ModuleB.CompressorB.summary.Ncomp}),
    StageNum_nd(
      fixed=false),
    CondExternalSystemKa_kPas2L2(
      fixed=false),
    ExternalSystemPressureDrop_Pa(
      fixed=false),
    CondExternalSystemPressureDrop_Pa(
      fixed=false),
    HighStaticFanExternalKa_kPas2L2(
      fixed={false,false,false,false}))
    annotation (Placement(transformation(extent={{-20.365866280640663,69.63413371935933},{8.365866280640663,98.36586628064067}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.Records.summary_system summary_system(
    Cooling_capacity=ModuleA.systemVariables.summary.CoolCap_total+ModuleB.systemVariables.summary.CoolCap_total,
    EER=(ModuleA.controlledCapacity+ModuleB.controlledCapacity)/(ModuleA.systemVariables.summary.pow_total+ModuleB.systemVariables.summary.pow_total),
    OAT=ECAT.AmbientAirDBTemp_K.setPoint-273.15,
    EWT=ECAT.EvapBrineEWT_K.setPoint-273.15,
    LWT=ECAT.EvapBrineLWT_K.setPoint-273.15,
    SST_A=ModuleA.nodeCpinA.summary.Tsat-273.15,
    SST_B=ModuleA.nodeCpinB.summary.Tsat-273.15,
    SST_C=ModuleB.nodeCpinA.summary.Tsat-273.15,
    SST_D=ModuleB.nodeCpinB.summary.Tsat-273.15,
    SDT_A=ModuleA.nodeCpoutA.summary.Tsat-273.15,
    SDT_B=ModuleA.nodeCpoutB.summary.Tsat-273.15,
    SDT_C=ModuleB.nodeCpoutA.summary.Tsat-273.15,
    SDT_D=ModuleB.nodeCpoutB.summary.Tsat-273.15,
    SET_A=ModuleA.nodeevapoutA.summary.Tsat-273.15,
    SET_B=ModuleA.nodeevapoutB.summary.Tsat-273.15,
    SET_C=ModuleB.nodeevapoutA.summary.Tsat-273.15,
    SET_D=ModuleB.nodeevapoutB.summary.Tsat-273.15,
    SCT_A=ModuleA.nodecondAirinA.summary.Tsat-273.15,
    SCT_B=ModuleA.nodecondAirinB.summary.Tsat-273.15,
    SCT_C=ModuleB.nodecondAirinA.summary.Tsat-273.15,
    SCT_D=ModuleB.nodecondAirinB.summary.Tsat-273.15,
    DGT_A=ModuleA.nodeCpoutA.summary.T-273.15,
    DGT_B=ModuleA.nodeCpoutB.summary.T-273.15,
    DGT_C=ModuleB.nodeCpoutA.summary.T-273.15,
    DGT_D=ModuleB.nodeCpoutB.summary.T-273.15,
    Compressor_power_A=ModuleA.CompressorA.summary.P_motor,
    Compressor_power_B=ModuleA.CompressorB.summary.P_motor,
    Compressor_power_C=ModuleB.CompressorA.summary.P_motor,
    Compressor_power_D=ModuleB.CompressorB.summary.P_motor,
    Fan_power_A=ModuleA.motorA.summary.power_motor,
    Fan_power_B=ModuleA.motorB.summary.power_motor,
    Fan_power_C=ModuleB.motorA.summary.power_motor,
    Fan_power_D=ModuleB.motorB.summary.power_motor,
    Fan_frequency_A=ModuleA.motorA.summary.Motor_freq,
    Fan_frequency_B=ModuleA.motorB.summary.Motor_freq,
    Fan_frequency_C=ModuleB.motorA.summary.Motor_freq,
    Fan_frequency_D=ModuleB.motorB.summary.Motor_freq,
    Compressor_frequency_A=ModuleA.CompressorA.summary.Ncomp,
    Compressor_frequency_B=ModuleA.CompressorB.summary.Ncomp,
    Compressor_frequency_C=ModuleB.CompressorA.summary.Ncomp,
    Compressor_frequency_D=ModuleB.CompressorB.summary.Ncomp,
    T_in_EXV_A=ModuleA.nodeEXVmaininA.summary.T-273.15,
    T_in_EXV_B=ModuleA.nodeEXVmaininB.summary.T-273.15,
    T_in_EXV_C=ModuleB.nodeEXVmaininA.summary.T-273.15,
    T_in_EXV_D=ModuleB.nodeEXVmaininB.summary.T-273.15,
    Subcooling_A=ModuleA.nodeEXVmaininA.summary.dTsh,
    Subcooling_B=ModuleA.nodeEXVmaininB.summary.dTsh,
    Subcooling_C=ModuleB.nodeEXVmaininA.summary.dTsh,
    Subcooling_D=ModuleB.nodeEXVmaininB.summary.dTsh,
    Pinch_A=ModuleA.evaporator.summary.LTD1,
    Pinch_B=ModuleA.evaporator.summary.LTD2,
    Pinch_C=ModuleB.evaporator.summary.LTD1,
    Pinch_D=ModuleB.evaporator.summary.LTD2,
    Vd_flow_rate=sink.summary.Vd,
    dP_evap=ModuleA.evaporator.summary.dp_coolant+ModuleB.evaporator.summary.dp_coolant,
    Z_U_cond_A=ModuleA.condAirA.summary.Z_U,
    Z_U_cond_B=ModuleA.condAirB.summary.Z_U,
    Z_cond_dpr_A=ModuleA.condAirA.summary.Z_dpr,
    Z_cond_dpr_B=ModuleA.condAirB.summary.Z_dpr,
    Z_U_eco_A=ModuleA.BPHEECOA.summary.ZUA,
    Z_U_eco_B=ModuleA.BPHEECOB.summary.ZUA,
    Z_U_evap_A=ModuleA.evaporator.summary.Z_Uev1,
    Z_U_evap_B=ModuleA.evaporator.summary.Z_Uev2,
    Z_dpc_evap=ModuleA.evaporator.summary.Z_dpc,
    Z_flow_comp_A=ModuleA.CompressorA.summary.Z_flow_suc,
    Z_flow_comp_B=ModuleA.CompressorB.summary.Z_flow_suc,
    Z_power_comp_A=ModuleA.CompressorA.summary.Z_power,
    Z_power_comp_B=ModuleA.CompressorB.summary.Z_power,
    Z_fan_A=ModuleA.motorA.summary.Z_power,
    Z_fan_B=ModuleA.motorB.summary.Z_power,
    Z_U_cond_C=ModuleB.condAirA.summary.Z_U,
    Z_U_cond_D=ModuleB.condAirB.summary.Z_U,
    Z_cond_dpr_C=ModuleB.condAirA.summary.Z_dpr,
    Z_cond_dpr_D=ModuleB.condAirB.summary.Z_dpr,
    Z_U_eco_C=ModuleB.BPHEECOA.summary.ZUA,
    Z_U_eco_D=ModuleB.BPHEECOB.summary.ZUA,
    Z_U_evap_C=ModuleB.evaporator.summary.Z_Uev1,
    Z_U_evap_D=ModuleB.evaporator.summary.Z_Uev2,
    Z_flow_comp_C=ModuleB.CompressorA.summary.Z_flow_suc,
    Z_flow_comp_D=ModuleB.CompressorB.summary.Z_flow_suc,
    Z_power_comp_C=ModuleB.CompressorA.summary.Z_power,
    Z_power_comp_D=ModuleB.CompressorB.summary.Z_power,
    Z_fan_C=ModuleB.motorA.summary.Z_power,
    Z_fan_D=ModuleB.motorB.summary.Z_power,
    Pump_frequency_A=ModuleA.pumpUser.summary.speed,
    Pump_frequency_B=ModuleB.pumpUser.summary.speed,
    Pump_power_A=ModuleA.pumpUser.summary.P_motor,
    Pump_power_B=ModuleB.pumpUser.summary.P_motor)
    annotation (Placement(transformation(extent={{16.448467277860907,74.4484672778609},{39.55153272213909,97.5515327221391}},origin={0.0,0.0},rotation=0.0)));
  output Real VI_A;
  output Real VI_B;
  output Real VI_C;
  output Real VI_D;
equation
  ECO_ON_A=
    if not choiceBlock.is60HZ and(ModuleA.nodeCpoutA.summary.Tsat > controllerSettings_moduleA_crkA.SDT_max-0.2) and ModuleA.OAT >(52+273.15) then
      false
    elseif to_degC(
      ModuleA.sourceAirA.summary.Tdb) < ECO_ON_slope*to_degC(
      ECAT.EvapBrineLWT_K.setPoint)+ECO_ON_inter then
      false
    else
      ModuleA.CompressorA.summary.PR > 2.3 and ModuleA.CompressorA.summary.Ncomp/ModuleA.NcompAMax > 0.3;
  ECO_ON_B=
    if not choiceBlock.is60HZ and(ModuleA.nodeCpoutB.summary.Tsat > controllerSettings_moduleA_crkB.SDT_max-0.2) and ModuleA.OAT >(52+273.15) then
      false
    elseif to_degC(
      ModuleA.sourceAirB.summary.Tdb) < ECO_ON_slope*to_degC(
      ECAT.EvapBrineLWT_K.setPoint)+ECO_ON_inter then
      false
    else
      ModuleA.CompressorB.summary.PR > 2.3 and ModuleA.CompressorB.summary.Ncomp/ModuleA.NcompBMax > 0.3;
  CAP_LOWsat=ModuleB.systemVariables.CoolCap_total < 0.23*ModuleB.globalParameters.capacity_design or Controller_Duplex.completeCompressorControlBase_duplex.capacity_controller_moduleB.summary.ID ==-3;
  VI_A=
    if choiceBlock.FAN_SPEED then
      .Workspace.Controller.StateMachine.isVIOn(
        ModuleA.Speed_crkA,
        95,
        20,
        ModuleA.nodeCpoutA.summary.Tsat,
        ModuleA.nodeCpinA.summary.Tsat)
    else
      .Workspace.Controller.StateMachine.isVIOn(
        ModuleA.Speed_crkA+15,
        95,
        20,
        ModuleA.nodeCpoutA.summary.Tsat,
        ModuleA.nodeCpinA.summary.Tsat);
  VI_ON_A=VI_A > 0.5;
  VI_B=VI_A;
  VI_ON_B=VI_ON_A;
  VI_C=
    if choiceBlock.FAN_SPEED then
      .Workspace.Controller.StateMachine.isVIOn(
        ModuleB.Speed_crkA,
        95,
        20,
        ModuleB.nodeCpoutA.summary.Tsat,
        ModuleB.nodeCpinA.summary.Tsat)
    else
      .Workspace.Controller.StateMachine.isVIOn(
        ModuleB.Speed_crkA+15,
        95,
        20,
        ModuleB.nodeCpoutA.summary.Tsat,
        ModuleB.nodeCpinA.summary.Tsat);
  VI_D=VI_C;
  if CAP_LOWsat then
    VI_ON_C=false;
    ECO_ON_C=false;
    VI_ON_D=VI_ON_C;
    ECO_ON_D=ECO_ON_C;
  else
    ECO_ON_C=
      if not choiceBlock.is60HZ and(ModuleB.nodeCpoutA.summary.Tsat > controllerSettings_moduleB_crkA.SDT_max-0.2) and ModuleB.OAT >(52+273.15) then
        false
      elseif to_degC(
        ModuleB.sourceAirA.summary.Tdb) < ECO_ON_slope*to_degC(
        ECAT.EvapBrineLWT_K.setPoint)+ECO_ON_inter then
       false
      else
        ModuleB.CompressorA.summary.PR > 2.3 and ModuleB.CompressorA.summary.Ncomp/ModuleB.NcompAMax > 0.3;
    ECO_ON_D=ECO_ON_C;
    VI_ON_C=VI_C > 0.5;
    VI_ON_D=VI_ON_C;
  end if;
  connect(ModuleA.measurementBusA,Controller_Duplex.measurementBus_moduleA_crkA)
    annotation (Line(points={{-63.28201563308103,10.55385131067814},{-63.28201563308103,0},{-29.311334619749985,0},{-29.311334619749985,33.14467961731743}},color={255,204,51}));
  connect(ModuleA.measurementBusB,Controller_Duplex.measurementBus_moduleA_crkB)
    annotation (Line(points={{-73.0035884104041,10.55385131067814},{-73.0035884104041,0},{-8.176083022539705,0},{-8.176083022539705,33.14467961731743}},color={255,204,51}));
  connect(ModuleA.measurementBusA,controllerSettings_moduleA_crkA.measurementBus)
    annotation (Line(points={{-63.28201563308103,10.55385131067814},{-63.28201563308103,2},{-106,2},{-106,49},{-80.4,49}},color={255,204,51}));
  connect(controllerSettings_moduleA_crkB.measurementBus,ModuleA.measurementBusB)
    annotation (Line(points={{-79.4,-54},{-106,-54},{-106,2},{-73.0035884104041,2},{-73.0035884104041,10.55385131067814}},color={255,204,51}));
  connect(Controller_Duplex.fan_moduleA_crkA,ModuleA.Fan_crkA)
    annotation (Line(points={{-35.962287919571395,30.336499335170608},{-44,30.336499335170608},{-44,7.613338290748111},{-53.02352301871851,7.613338290748111}},color={0,0,127}));
  connect(ModuleA.EXV_Eco_crkA,Controller_Duplex.ecoExv_moduleA_crkA)
    annotation (Line(points={{-53.02352301871851,-0.2690180151895216},{-44,-0.2690180151895216},{-44,27.380520090805536},{-35.962287919571395,27.380520090805536}},color={0,0,127}));
  connect(ModuleA.actuatorSSTmaxA,Controller_Duplex.sstmax_moduleA_crkA)
    annotation (Line(points={{-53.02352301871851,2.358434086789689},{-44,2.358434086789689},{-44,24.42454084644046},{-35.962287919571395,24.42454084644046}},color={0,0,127}));
  connect(ModuleA.EXV_Main_crkA,Controller_Duplex.exv_moduleA_crkA)
    annotation (Line(points={{-53.02352301871851,4.9858861887689},{-44,4.9858861887689},{-44,21.46856160207539},{-35.962287919571395,21.46856160207539}},color={0,0,127}));
  connect(ModuleA.Speed_crkA,Controller_Duplex.compressor_moduleA_crkA)
    annotation (Line(points={{-53.02352301871851,-2.8964701171687315},{-44,-2.8964701171687315},{-44,18.512582357710315},{-35.962287919571395,18.512582357710315}},color={0,0,127}));
  connect(Controller_Duplex.compressor_moduleA_crkB,ModuleA.Speed_crkB)
    annotation (Line(points={{-35.962287919571395,15.556603113345242},{-44,15.556603113345242},{-44,-5.523922219147942},{-53.02352301871851,-5.523922219147942}},color={0,0,127}));
  connect(ModuleA.Fan_crkB,Controller_Duplex.fan_moduleA_crkB)
    annotation (Line(points={{-53.02352301871851,-8.151374321127152},{-44,-8.151374321127152},{-44,12.600623868980167},{-35.962287919571395,12.600623868980167}},color={0,0,127}));
  connect(ModuleA.EXV_Main_crkB,Controller_Duplex.exv_moduleA_crkB)
    annotation (Line(points={{-53.02352301871851,-10.778826423106363},{-44,-10.778826423106363},{-44,9.644644624615093},{-35.962287919571395,9.644644624615093}},color={0,0,127}));
  connect(ModuleA.actuatorSSTmaxB,Controller_Duplex.sstmax_moduleA_crkB)
    annotation (Line(points={{-53.02352301871851,-13.406278525085572},{-44,-13.406278525085572},{-44,6.688665380250017},{-35.962287919571395,6.688665380250017}},color={0,0,127}));
  connect(Controller_Duplex.ecoExv_moduleA_crkB,ModuleA.EXV_Eco_crkB)
    annotation (Line(points={{-35.962287919571395,3.732686135884947},{-44,3.732686135884947},{-44,-16.033730627064784},{-53.02352301871851,-16.033730627064784}},color={0,0,127}));
  connect(ModuleB.measurementBusA,Controller_Duplex.measurementBus_moduleB_crkA)
    annotation (Line(points={{25.48642447525767,11.923299010317415},{25.48642447525767,-0.11008688178965187},{-29.311334619749985,-0.11008688178965187}},color={255,204,51}));
  connect(ModuleB.measurementBusB,Controller_Duplex.measurementBus_moduleB_crkB)
    annotation (Line(points={{34.78680564171361,11.923299010317415},{34.78680564171361,-0.11008688178965187},{-8.176083022539705,-0.11008688178965187}},color={255,204,51}));
  connect(ModuleB.EXV_Main_crkA,Controller_Duplex.exv_moduleB_crkA)
    annotation (Line(points={{15.672385770594897,6.596568537643062},{3.8173368450831484,6.596568537643062},{3.8173368450831484,30.48429829738886},{-4.0377120804286015,30.48429829738886}},color={0,0,127}));
  connect(Controller_Duplex.sstmax_moduleB_crkA,ModuleB.actuatorSSTmaxA)
    annotation (Line(points={{-4.0377120804286015,27.528319053023786},{3.8173368450831484,27.528319053023786},{3.8173368450831484,4.082952006168481},{15.672385770594897,4.082952006168481}},color={0,0,127}));
  connect(ModuleB.EXV_Eco_crkA,Controller_Duplex.ecoExv_moduleB_crkA)
    annotation (Line(points={{15.672385770594897,1.5693354746939026},{3.8173368450831484,1.5693354746939026},{3.8173368450831484,24.572339808658715},{-4.0377120804286015,24.572339808658715}},color={0,0,127}));
  connect(ModuleB.Speed_crkA,Controller_Duplex.compressor_moduleB_crkA)
    annotation (Line(points={{15.672385770594897,-0.9442810567806768},{3.8173368450831484,-0.9442810567806768},{3.8173368450831484,21.61636056429364},{-4.0377120804286015,21.61636056429364}},color={0,0,127}));
  connect(Controller_Duplex.compressor_moduleB_crkB,ModuleB.Speed_crkB)
    annotation (Line(points={{-4.0377120804286015,18.660381319928568},{3.8173368450831484,18.660381319928568},{3.8173368450831484,-3.4578975882552565},{15.672385770594897,-3.4578975882552565}},color={0,0,127}));
  connect(Controller_Duplex.fan_moduleB_crkB,ModuleB.Fan_crkB)
    annotation (Line(points={{-4.0377120804286015,15.704402075563493},{3.8173368450831484,15.704402075563493},{3.8173368450831484,-5.971514119729836},{15.672385770594897,-5.971514119729836}},color={0,0,127}));
  connect(ModuleB.EXV_Main_crkB,Controller_Duplex.exv_moduleB_crkB)
    annotation (Line(points={{15.672385770594897,-8.485130651204415},{3.8173368450831484,-8.485130651204415},{3.8173368450831484,12.748422831198418},{-4.0377120804286015,12.748422831198418}},color={0,0,127}));
  connect(ModuleB.actuatorSSTmaxB,Controller_Duplex.sstmax_moduleB_crkB)
    annotation (Line(points={{15.672385770594897,-10.998747182678994},{3.8173368450831484,-10.998747182678994},{3.8173368450831484,9.792443586833345},{-4.0377120804286015,9.792443586833345}},color={0,0,127}));
  connect(Controller_Duplex.ecoExv_moduleB_crkB,ModuleB.EXV_Eco_crkB)
    annotation (Line(points={{-4.0377120804286015,6.836464342468272},{3.8173368450831484,6.836464342468272},{3.8173368450831484,-13.512363714153572},{15.672385770594897,-13.512363714153572}},color={0,0,127}));
  connect(Controller_Duplex.fan_moduleB_crkA,ModuleB.Fan_crkA)
    annotation (Line(points={{-4.0377120804286015,3.8804850981031986},{3.8173368450831484,3.8804850981031986},{3.8173368450831484,9.110185069117641},{15.672385770594897,9.110185069117641}},color={0,0,127}));
  connect(Controller_Duplex.measurementBus_moduleB_crkB,controllerSettings_moduleB_crkB.measurementBus)
    annotation (Line(points={{-8.176083022539705,-0.11008688178965187},{44,-0.11008688178965187},{44,-54},{20.6,-54}},color={255,204,51}));
  connect(Controller_Duplex.measurementBus_moduleB_crkA,controllerSettings_moduleB_crkA.measurementBus)
    annotation (Line(points={{-29.311334619749985,-0.11008688178965187},{-29.311334619749985,47},{17.6,47}},color={255,204,51}));
  connect(ModuleA.measurementBusA,Controller_Duplex.measurementBus_moduleA_crkA)
    annotation (Line(points={{-63.28201563308103,9.55385131067814},{-63.28201563308103,0},{-29.311334619749985,0},{-29.311334619749985,33.14467961731743}},color={255,204,51}));
  connect(ModuleA.measurementBusB,Controller_Duplex.measurementBus_moduleA_crkB)
    annotation (Line(points={{-73.0035884104041,9.55385131067814},{-73.0035884104041,0},{-8.176083022539705,0},{-8.176083022539705,33.14467961731743}},color={255,204,51}));
  connect(source.port,ModuleA.port_a)
    annotation (Line(points={{-125.41713641670623,0},{-56,0},{-56,-4.210196168158337},{-52.91842493463933,-4.210196168158337}},color={0,127,0}));
  connect(node.port_a,ModuleA.port_b)
    annotation (Line(points={{-26.546017779007418,-32},{-44,-32},{-44,-4.210196168158337},{-83.13412410740025,-4.210196168158337}},color={0,127,0}));
  connect(node.port_b,ModuleB.port_a)
    annotation (Line(points={{-13.453982220992582,-32},{4,-32},{4,-2.2010893225179666},{15.571841109335914,-2.2010893225179666}},color={0,127,0}));
  connect(ModuleB.port_b,sink.port)
    annotation (Line(points={{44.478431221293576,-2.2010893225179666},{38,-2.2010893225179666},{38,-2},{86.0819150036863,-2}},color={0,127,0}));
  connect(Controller_Duplex.KaInput_crk_moduleA,ModuleA.ActuatorKaInput)
    annotation (Line(points={{-35.962287919571395,0.7767068915198756},{-44,0.7767068915198756},{-44,12.868242494706529},{-53.02352301871851,12.868242494706529}},color={0,0,127}));
  connect(Controller_Duplex.pumpUser_crk_moduleA,ModuleA.ActuatorPumpUser)
    annotation (Line(points={{-35.962287919571395,-2.1792723528451994},{-44,-2.1792723528451994},{-44,10.24079039272732},{-53.02352301871851,10.24079039272732}},color={0,0,127}));
  connect(Controller_Duplex.KaInput_crk_moduleB,ModuleB.ActuatorKaInput)
    annotation (Line(points={{-4.037712080428605,0.9245058537381254},{4,0.9245058537381254},{4,14.137418132066802},{15.672385770594897,14.137418132066802}},color={0,0,127}));
  connect(Controller_Duplex.pumpUser_crk_moduleB,ModuleB.ActuatorPumpUser)
    annotation (Line(points={{-4.037712080428605,-2.0314733906269495},{4,-2.0314733906269495},{4,11.623801600592222},{15.672385770594897,11.623801600592222}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={245,166,35},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end System_30XBV;
